2025-08-24 18:06:24.398|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 6460 (D:\RFP\grfp-project\grfp-api\target\classes started by <PERSON>ham<PERSON>od in D:\RFP\grfp-project)  [] ------
2025-08-24 18:06:24.404|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-24 18:06:27.351|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-24 18:06:27.358|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-24 18:06:27.464|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 74ms. Found 0 Redis repository interfaces.  [] ------
2025-08-24 18:06:29.320|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-24 18:06:29.343|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-24 18:06:29.343|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-24 18:06:29.587|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-24 18:06:29.587|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 5051 ms  [] ------
2025-08-24 18:06:30.023|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-24 18:06:41.898|main|ERROR|o.s.boot.web.embedded.tomcat.TomcatStarter.onStartup:61|Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'sessionRepositoryFilterRegistration' defined in class path resource [org/springframework/boot/autoconfigure/session/SessionRepositoryFilterConfiguration.class]: Unsatisfied dependency expressed through method 'sessionRepositoryFilterRegistration' parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: *************/*************:6379  [] ------
2025-08-24 18:06:41.990|main| INFO|org.apache.catalina.core.StandardService.log:173|Stopping service [Tomcat]  [] ------
2025-08-24 18:06:42.010|main| WARN|org.apache.catalina.loader.WebappClassLoaderBase.log:173|The web application [grfp-api] appears to have started a thread named [Thread-16] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)
 sun.net.dns.ResolverConfigurationImpl$AddressChangeListener.run(ResolverConfigurationImpl.java:144)  [] ------
2025-08-24 18:06:42.012|main| WARN|org.apache.catalina.loader.WebappClassLoaderBase.log:173|The web application [grfp-api] appears to have started a thread named [logback-5] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)  [] ------
2025-08-24 18:06:42.019|main| WARN|o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext.refresh:559|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat  [] ------
2025-08-24 18:06:42.039|main| INFO|o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.  [] ------
2025-08-24 18:06:42.125|main|ERROR|org.springframework.boot.SpringApplication.reportFailure:834|Application run failed org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:161)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:545)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:142)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:440)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:193)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:178)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:158)
	... 9 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sessionRepositoryFilterRegistration' defined in class path resource [org/springframework/boot/autoconfigure/session/SessionRepositoryFilterConfiguration.class]: Unsatisfied dependency expressed through method 'sessionRepositoryFilterRegistration' parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: *************/*************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:799)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:540)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:202)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:85)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:255)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:229)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5161)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1384)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1374)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:909)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:843)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1384)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1374)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:909)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:262)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:930)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:486)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	... 14 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: *************/*************:6379
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:886)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:790)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: *************/*************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:799)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:540)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getObject(DefaultListableBeanFactory.java:1906)
	at org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration.setRedisConnectionFactory(RedisHttpSessionConfiguration.java:209)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:725)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1425)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	... 75 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: *************/*************:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:886)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:790)
	... 97 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: *************/*************:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:652)
	... 111 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: *************/*************:6379
	at org.redisson.connection.pool.ConnectionPool$1.lambda$run$0(ConnectionPool.java:158)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:552)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:328)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:294)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:571)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:550)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.client.RedisClient$2$2.run(RedisClient.java:251)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: *************/*************:6379
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 8 common frames omitted
 [] ------
2025-08-24 19:18:47.532|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 23664 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-24 19:18:47.536|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-24 19:18:49.864|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-24 19:18:49.868|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-24 19:18:49.931|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 43ms. Found 0 Redis repository interfaces.  [] ------
2025-08-24 19:18:51.518|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-24 19:18:51.540|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-24 19:18:51.540|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-24 19:18:51.774|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-24 19:18:51.775|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 4045 ms  [] ------
2025-08-24 19:18:52.197|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-24 19:18:54.368|main|ERROR|o.s.boot.web.embedded.tomcat.TomcatStarter.onStartup:61|Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'sessionRepositoryFilterRegistration' defined in class path resource [org/springframework/boot/autoconfigure/session/SessionRepositoryFilterConfiguration.class]: Unsatisfied dependency expressed through method 'sessionRepositoryFilterRegistration' parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379  [] ------
2025-08-24 19:18:54.427|main| INFO|org.apache.catalina.core.StandardService.log:173|Stopping service [Tomcat]  [] ------
2025-08-24 19:18:54.442|main| WARN|org.apache.catalina.loader.WebappClassLoaderBase.log:173|The web application [grfp-api] appears to have started a thread named [Thread-14] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)
 sun.net.dns.ResolverConfigurationImpl$AddressChangeListener.run(ResolverConfigurationImpl.java:144)  [] ------
2025-08-24 19:18:54.448|main| WARN|o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext.refresh:559|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat  [] ------
2025-08-24 19:18:54.463|main| INFO|o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.  [] ------
2025-08-24 19:18:54.505|main|ERROR|org.springframework.boot.SpringApplication.reportFailure:834|Application run failed org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:161)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:545)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:142)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:440)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:193)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:178)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:158)
	... 9 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sessionRepositoryFilterRegistration' defined in class path resource [org/springframework/boot/autoconfigure/session/SessionRepositoryFilterConfiguration.class]: Unsatisfied dependency expressed through method 'sessionRepositoryFilterRegistration' parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:799)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:540)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:202)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:85)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:255)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:229)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5161)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1384)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1374)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:909)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:843)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1384)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1374)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:909)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:262)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:930)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:486)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	... 14 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:886)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:790)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:799)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:540)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getObject(DefaultListableBeanFactory.java:1906)
	at org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration.setRedisConnectionFactory(RedisHttpSessionConfiguration.java:209)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:725)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1425)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	... 75 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:886)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:790)
	... 97 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:652)
	... 111 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.redisson.connection.pool.ConnectionPool$1.lambda$run$0(ConnectionPool.java:158)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:552)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:328)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:294)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:571)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:550)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.client.RedisClient$2$1.run(RedisClient.java:242)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.redisson.client.RedisException: ERR Client sent AUTH, but no password is set. channel: [id: 0xc2f3756a, L:/10.8.10.58:55439 - R:***********/***********:6379] command: (AUTH), params: (password masked)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:345)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:177)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:116)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:101)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:508)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	... 4 common frames omitted
 [] ------
2025-08-24 19:21:02.555|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 24724 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-24 19:21:02.558|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-24 19:21:04.700|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-24 19:21:04.704|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-24 19:21:04.768|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 43ms. Found 0 Redis repository interfaces.  [] ------
2025-08-24 19:21:06.572|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-24 19:21:06.596|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-24 19:21:06.596|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-24 19:21:06.863|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-24 19:21:06.864|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 4191 ms  [] ------
2025-08-24 19:21:07.327|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-24 19:21:09.618|redisson-netty-2-28| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for ***********/***********:6379  [] ------
2025-08-24 19:21:10.551|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for ***********/***********:6379  [] ------
2025-08-24 19:21:15.829|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-24 19:21:17.330|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-24 19:21:18.400|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-24 19:21:20.355|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-24 19:21:21.204|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-24 19:21:21.205|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-24 19:21:21.304|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-24 19:21:22.797|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-24 19:21:28.611|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-24 19:21:29.213|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 27.848 seconds (JVM running for 31.551)  [] ------
2025-08-24 19:21:29.234|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-24 19:21:29.627|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-24 19:21:31.382|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-24 19:21:32.464|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-24 19:21:33.251|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-24 19:21:33.773|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-24 19:21:34.231|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-24 19:21:49.450|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-24 19:22:04.759|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-24 19:28:29.604|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-24 19:28:29.604|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-24 19:28:29.625|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 20 ms  [] ------
2025-08-24 19:28:30.074|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/UploadStandardBid?projectId=120]  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:29:10.090|http-nio-6888-exec-1| WARN|com.volcengine.tos.printHighLatencyLog:227|[high latency request] requestId: 8b9701aaf7857e5068aaf785-b3a10e0-1uq8uL-PuO-cg-tos-1az-front-azc-1, method: PUT, host: grfp-dev-public.tos-cn-guangzhou.volces.com, request uri: /excel%2Fimport%2F68aaf783e9d24605f9b34973.xlsx, dns cost: 81 ms, connect cost: 1694 ms, tls handshake cost: 1599 ms, send headers and body cost: 326 ms, wait response cost: 20 ms, request cost: 2459 ms
  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:29:10.091|http-nio-6888-exec-1| INFO|com.volcengine.tos.printAccessLogSucceed:262|tos: status code:200, request id:8b9701aaf7857e5068aaf785-b3a10e0-1uq8uL-PuO-cg-tos-1az-front-azc-1, request cost 2604 ms, request 1 times
  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:29:22.823|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 52041  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:29:23.038|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"id":288},"message":"Succeeded","successful":true}  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:29:48.125|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/UploadStandardBid?projectId=120]  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:30:15.053|general-async-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [144084]  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:30:31.546|sync-glink-async-1| INFO|com.fangcang.grfp.core.manager.HotelManager.syncHotelByHotelEntityList:222|syncHotelByHotelEntityList begin  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:30:31.549|sync-glink-async-1| INFO|com.fangcang.grfp.core.manager.HotelManager.syncHotelInfo:82|Start sync hotel info, hotel ids: [144084]  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:32:59.265|general-async-1| INFO|com.fangcang.grfp.core.manager.HotelManager.queryHotelBasicInfo:171|queryHotelBasicInfo: [144084]  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:32:59.591|sync-glink-async-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[144084],"settings":["hotelFacilityNew","breakfast","importantNotices","parking","chargingParking","hotelCertificates","comment","hotelMeetingInfos","hotelVideoInfos","hotelTextPolicies","hotelStructuredPolicies.childPolicy","hotelStructuredPolicies.extraBedPolicy","hotelStructuredPolicies.petPolicy"]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"37C295543FA9841AD47427B78BF18D47","timestamp":1756035082513,"version":"1.0.0"}}  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:32:59.670|sync-glink-async-2| INFO|com.fangcang.grfp.core.manager.HotelManager.syncHotelByHotelEntityList:222|syncHotelByHotelEntityList begin  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:32:59.670|sync-glink-async-2| INFO|com.fangcang.grfp.core.manager.HotelManager.syncHotelInfo:82|Start sync hotel info, hotel ids: [144084]  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:05.889|general-async-1| INFO|com.fangcang.grfp.core.manager.BidManager.lambda$null$0:121|开始创建报价, projectId: 120, hotelId: 144084  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:07.864|sync-glink-async-2| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"en-US","hotelIds":[144084],"settings":["hotelFacilityNew","breakfast","importantNotices","parking","chargingParking","hotelCertificates","comment","hotelMeetingInfos","hotelVideoInfos","hotelTextPolicies","hotelStructuredPolicies.childPolicy","hotelStructuredPolicies.extraBedPolicy","hotelStructuredPolicies.petPolicy"]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"8B69FF189878917F1FC55A4B9E818FF3","timestamp":1756035185889,"version":"1.0.0"}}  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:11.852|sync-glink-async-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[144084],"settings":["hotelFacilityNew","breakfast","importantNotices","parking","chargingParking","hotelCertificates","comment","hotelMeetingInfos","hotelVideoInfos","hotelTextPolicies","hotelStructuredPolicies.childPolicy","hotelStructuredPolicies.extraBedPolicy","hotelStructuredPolicies.petPolicy"]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"74D5D2E2CCB722C16FEA34E4B1712B8C","timestamp":1756035190899,"version":"1.0.0"}}  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:12.290|sync-glink-async-2| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelInfo, Request Body: {"businessRequest":{"language":"zh-CN","hotelIds":[144084],"settings":["hotelFacilityNew","breakfast","importantNotices","parking","chargingParking","hotelCertificates","comment","hotelMeetingInfos","hotelVideoInfos","hotelTextPolicies","hotelStructuredPolicies.childPolicy","hotelStructuredPolicies.extraBedPolicy","hotelStructuredPolicies.petPolicy"]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelInfo","signature":"37F53ED319243DD59D0858035B5498B3","timestamp":1756035192110,"version":"1.0.0"}}  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:14.265|sync-glink-async-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelImage, Request Body: {"businessRequest":{"hotelIds":[144084]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelImage","signature":"39B2505994C77E8F6FCB30853B765A38","timestamp":1756035194084,"version":"1.0.0"}}  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:15.475|sync-glink-async-2| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelImage, Request Body: {"businessRequest":{"hotelIds":[144084]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelImage","signature":"FBBA9D339AB35F8CB05A0DDF8E7A3DDF","timestamp":1756035195330,"version":"1.0.0"}}  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:18.240|sync-glink-async-1| INFO|com.fangcang.grfp.core.manager.HotelManager.syncHotelInfo:144|End sync hotel info, active hotel ids: [144084]  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:18.240|sync-glink-async-2| INFO|com.fangcang.grfp.core.manager.HotelManager.syncHotelInfo:144|End sync hotel info, active hotel ids: [144084]  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:18.240|sync-glink-async-1| INFO|com.fangcang.grfp.core.manager.HotelManager.syncHotelByHotelEntityList:229|syncHotelByHotelEntityList end  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:18.240|sync-glink-async-2| INFO|com.fangcang.grfp.core.manager.HotelManager.syncHotelByHotelEntityList:229|syncHotelByHotelEntityList end  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:24.983|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x7f074e84, L:/10.8.10.58:55724 - R:***********/***********:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://***********:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-24 19:33:25.375|general-async-1| INFO|com.fangcang.grfp.core.manager.ExcelManager.asyncImport:220|导入记录已更新，recordId: 288  [68aaf75ee9d24605f9b34972] ------
2025-08-24 19:33:29.356|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x23032ea4, L:/10.8.10.58:55730 - R:***********/***********:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://***********:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-24 19:43:48.704|http-nio-6888-exec-2| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68aafaf4e9d24605f9b34974] ------
2025-08-24 19:43:48.796|http-nio-6888-exec-2| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"projectId":120,"requestSequenceNo":123456}  [68aafaf4e9d24605f9b34974] ------
2025-08-24 19:43:53.149|http-nio-6888-exec-2| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 3922  [68aafaf4e9d24605f9b34974] ------
2025-08-24 19:43:53.248|http-nio-6888-exec-2| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68aafaf4e9d24605f9b34974] ------
2025-08-24 19:44:45.857|http-nio-6888-exec-4| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68aafb2de9d24605f9b34975] ------
2025-08-24 19:44:46.190|http-nio-6888-exec-4| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"projectId":120,"requestSequenceNo":123456}  [68aafb2de9d24605f9b34975] ------
2025-08-24 19:45:06.047|http-nio-6888-exec-4| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 19857  [68aafb2de9d24605f9b34975] ------
2025-08-24 19:45:06.199|http-nio-6888-exec-4| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68aafb2de9d24605f9b34975] ------
2025-08-24 19:45:22.676|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68aafb52e9d24605f9b34976] ------
2025-08-24 19:45:22.858|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"projectId":120,"requestSequenceNo":123456}  [68aafb52e9d24605f9b34976] ------
2025-08-24 19:45:35.220|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 12360  [68aafb52e9d24605f9b34976] ------
2025-08-24 19:45:35.631|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68aafb52e9d24605f9b34976] ------
2025-08-24 19:50:50.369|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68aafc9ae9d24605f9b34977] ------
2025-08-24 19:50:50.431|http-nio-6888-exec-6| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"projectId":120,"requestSequenceNo":123456}  [68aafc9ae9d24605f9b34977] ------
2025-08-24 19:52:29.679|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x4dfe0f2f, L:/10.8.10.58:55729 - R:***********/***********:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://***********:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-24 19:53:37.314|HikariPool-1 housekeeper| WARN|com.zaxxer.hikari.pool.HikariPool.run:787|HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m7s873ms660µs400ns).  [] ------
2025-08-24 19:53:39.269|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 168837  [68aafc9ae9d24605f9b34977] ------
2025-08-24 19:53:39.380|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68aafc9ae9d24605f9b34977] ------
2025-08-24 20:02:23.366|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 26492 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-24 20:02:23.369|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-24 20:02:25.304|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-24 20:02:25.308|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-24 20:02:25.366|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 40ms. Found 0 Redis repository interfaces.  [] ------
2025-08-24 20:02:26.941|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-24 20:02:26.962|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-24 20:02:26.963|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-24 20:02:27.220|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-24 20:02:27.221|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3763 ms  [] ------
2025-08-24 20:02:27.616|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-24 20:02:30.026|redisson-netty-2-27| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for ***********/***********:6379  [] ------
2025-08-24 20:02:30.834|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for ***********/***********:6379  [] ------
2025-08-24 20:02:35.202|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-24 20:02:36.397|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-24 20:02:37.020|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-24 20:02:38.308|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-24 20:02:38.871|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-24 20:02:38.872|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-24 20:02:38.959|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-24 20:02:40.235|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-24 20:02:45.294|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-24 20:02:45.890|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 23.57 seconds (JVM running for 26.106)  [] ------
2025-08-24 20:02:45.900|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-24 20:02:46.138|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-24 20:02:47.348|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-24 20:02:48.597|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-24 20:02:49.492|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-24 20:02:49.963|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-24 20:02:50.500|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-24 20:03:04.150|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-24 20:03:17.941|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-24 23:13:58.137|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-24 23:13:58.137|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-24 23:13:58.137|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'generalExecutor'  [] ------
2025-08-24 23:13:58.148|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'auditLogExecutor'  [] ------
2025-08-24 23:13:58.152|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-24 23:13:58.152|SpringContextShutdownHook| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-24 23:13:58.152|SpringContextShutdownHook| INFO|com.fangcang.grfp.api.GRfpApiApplication.onDestroy:71|GrfpApi v1.0.0-20250808 is already [***stopped***]  [] ------
2025-08-24 23:13:58.155|SpringContextShutdownHook| INFO|com.zaxxer.hikari.HikariDataSource.close:350|HikariPool-1 - Shutdown initiated...  [] ------
2025-08-24 23:14:04.392|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 24920 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-24 23:14:04.395|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-24 23:14:06.423|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-24 23:14:06.427|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-24 23:14:06.494|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 45ms. Found 0 Redis repository interfaces.  [] ------
2025-08-24 23:14:07.965|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-24 23:14:07.984|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-24 23:14:07.985|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-24 23:14:08.190|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-24 23:14:08.191|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3653 ms  [] ------
2025-08-24 23:14:08.578|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-24 23:14:10.807|redisson-netty-2-17| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for ***********/***********:6379  [] ------
2025-08-24 23:14:11.665|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for ***********/***********:6379  [] ------
2025-08-24 23:14:15.973|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-24 23:14:17.036|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-24 23:14:17.695|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-24 23:14:19.252|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-24 23:14:19.745|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-24 23:14:19.746|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-24 23:14:19.822|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-24 23:14:20.945|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-24 23:14:25.491|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-24 23:14:26.195|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 22.386 seconds (JVM running for 24.277)  [] ------
2025-08-24 23:14:26.206|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-24 23:14:26.468|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-24 23:14:48.757|main|ERROR|com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException:593|HikariPool-1 - Exception during pool initialization. com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:358)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:477)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:560)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy270.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy112.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy119.selectList(Unknown Source)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap(CachedHotelBrandServiceImpl.java:37)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$FastClassBySpringCGLIB$$e4014f52.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:53)
	at org.springframework.cache.interceptor.CacheAspectSupport.invokeOperation(CacheAspectSupport.java:366)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:421)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:346)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$EnhancerBySpringCGLIB$$e7e3ba23.getNameMap(<generated>)
	at com.fangcang.grfp.api.GRfpApiApplication.afterStarted(GRfpApiApplication.java:60)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:305)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:190)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:153)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:404)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:361)
	at org.springframework.boot.context.event.EventPublishingRunListener.running(EventPublishingRunListener.java:108)
	at org.springframework.boot.SpringApplicationRunListeners.running(SpringApplicationRunListeners.java:77)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:327)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 73 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 76 common frames omitted
 [] ------
2025-08-24 23:14:48.809|main|ERROR|org.springframework.boot.SpringApplication.reportFailure:834|Application run failed org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/fangcang/grfp/core/mapper/HotelBrandMapper.java (best guess)
### The error may involve com.fangcang.grfp.core.mapper.HotelBrandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy112.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy119.selectList(Unknown Source)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap(CachedHotelBrandServiceImpl.java:37)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$FastClassBySpringCGLIB$$e4014f52.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:53)
	at org.springframework.cache.interceptor.CacheAspectSupport.invokeOperation(CacheAspectSupport.java:366)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:421)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:346)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.core.cached.impl.CachedHotelBrandServiceImpl$$EnhancerBySpringCGLIB$$e7e3ba23.getNameMap(<generated>)
	at com.fangcang.grfp.api.GRfpApiApplication.afterStarted(GRfpApiApplication.java:60)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:305)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:190)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:153)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:404)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:361)
	at org.springframework.boot.context.event.EventPublishingRunListener.running(EventPublishingRunListener.java:108)
	at org.springframework.boot.SpringApplicationRunListeners.running(SpringApplicationRunListeners.java:77)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:327)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in com/fangcang/grfp/core/mapper/HotelBrandMapper.java (best guess)
### The error may involve com.fangcang.grfp.core.mapper.HotelBrandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 41 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy270.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 48 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:358)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:477)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:560)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	... 60 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 73 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 76 common frames omitted
 [] ------
2025-08-24 23:14:49.216|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-24 23:14:49.217|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-24 23:14:49.218|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'generalExecutor'  [] ------
2025-08-24 23:14:49.239|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'auditLogExecutor'  [] ------
2025-08-24 23:14:49.244|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-24 23:14:49.245|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:218|Shutting down ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-24 23:14:49.245|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.onDestroy:71|GrfpApi v1.0.0-20250808 is already [***stopped***]  [] ------
