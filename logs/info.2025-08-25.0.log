2025-08-25 22:55:32.875|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 18728 (D:\RFP\grfp-project\grfp-api\target\classes started by <PERSON>hamGod in D:\RFP\grfp-project)  [] ------
2025-08-25 22:55:32.881|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-25 22:55:35.049|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-25 22:55:35.053|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-25 22:55:35.115|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 40ms. Found 0 Redis repository interfaces.  [] ------
2025-08-25 22:55:36.593|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-25 22:55:36.612|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-25 22:55:36.612|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-25 22:55:36.795|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-25 22:55:36.795|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3824 ms  [] ------
2025-08-25 22:55:37.166|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-25 22:55:39.125|redisson-netty-2-25| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for ***********/***********:6379  [] ------
2025-08-25 22:55:39.965|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for ***********/***********:6379  [] ------
2025-08-25 22:55:43.911|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-25 22:55:44.996|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-25 22:55:45.499|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-25 22:55:46.657|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-25 22:55:47.196|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-25 22:55:47.197|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-25 22:55:47.279|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-25 22:55:48.503|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
