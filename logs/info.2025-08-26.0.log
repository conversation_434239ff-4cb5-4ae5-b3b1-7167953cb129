2025-08-26 11:22:16.381|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 22636 (D:\RFP\grfp-project\grfp-api\target\classes started by <PERSON>ham<PERSON>od in D:\RFP\grfp-project)  [] ------
2025-08-26 11:22:16.385|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-26 11:22:17.946|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-26 11:22:17.949|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-26 11:22:18.002|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.  [] ------
2025-08-26 11:22:19.201|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-26 11:22:19.216|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-26 11:22:19.216|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-26 11:22:19.386|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-26 11:22:19.386|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2932 ms  [] ------
2025-08-26 11:22:19.701|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-26 11:22:31.143|main|ERROR|o.s.boot.web.embedded.tomcat.TomcatStarter.onStartup:61|Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'sessionRepositoryFilterRegistration' defined in class path resource [org/springframework/boot/autoconfigure/session/SessionRepositoryFilterConfiguration.class]: Unsatisfied dependency expressed through method 'sessionRepositoryFilterRegistration' parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379  [] ------
2025-08-26 11:22:31.186|main| INFO|org.apache.catalina.core.StandardService.log:173|Stopping service [Tomcat]  [] ------
2025-08-26 11:22:31.199|main| WARN|org.apache.catalina.loader.WebappClassLoaderBase.log:173|The web application [grfp-api] appears to have started a thread named [Thread-10] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)
 sun.net.dns.ResolverConfigurationImpl$AddressChangeListener.run(ResolverConfigurationImpl.java:144)  [] ------
2025-08-26 11:22:31.200|main| WARN|org.apache.catalina.loader.WebappClassLoaderBase.log:173|The web application [grfp-api] appears to have started a thread named [logback-5] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)  [] ------
2025-08-26 11:22:31.204|main| WARN|o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext.refresh:559|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat  [] ------
2025-08-26 11:22:31.215|main| INFO|o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.  [] ------
2025-08-26 11:22:31.249|main|ERROR|org.springframework.boot.SpringApplication.reportFailure:834|Application run failed org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:161)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:545)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.fangcang.grfp.api.GRfpApiApplication.main(GRfpApiApplication.java:46)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:142)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:440)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:193)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:178)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:158)
	... 9 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sessionRepositoryFilterRegistration' defined in class path resource [org/springframework/boot/autoconfigure/session/SessionRepositoryFilterConfiguration.class]: Unsatisfied dependency expressed through method 'sessionRepositoryFilterRegistration' parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:799)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:540)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:202)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:85)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:255)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:229)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5161)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1384)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1374)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:909)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:843)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1384)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1374)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:909)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:262)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:930)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:486)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	... 14 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.session.RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration': Initialization of bean failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:886)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:790)
	... 56 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'redissonConnectionFactory' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Unsatisfied dependency expressed through method 'redissonConnectionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:799)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:540)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory$DependencyObjectProvider.getObject(DefaultListableBeanFactory.java:1906)
	at org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration.setRedisConnectionFactory(RedisHttpSessionConfiguration.java:209)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:725)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1425)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	... 75 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [org/redisson/spring/starter/RedissonAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:886)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:790)
	... 97 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:652)
	... 111 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: ***********/***********:6379
	at org.redisson.connection.pool.ConnectionPool$1.lambda$run$0(ConnectionPool.java:158)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:552)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:328)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:294)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:571)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:550)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.client.RedisClient$3$2.run(RedisClient.java:312)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: ***********/***********:6379
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 8 common frames omitted
 [] ------
2025-08-26 11:43:22.432|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 21080 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-26 11:43:22.435|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-26 11:43:24.433|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-26 11:43:24.437|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-26 11:43:24.508|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 48ms. Found 0 Redis repository interfaces.  [] ------
2025-08-26 11:43:26.099|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-26 11:43:26.117|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-26 11:43:26.118|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-26 11:43:26.328|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-26 11:43:26.328|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3798 ms  [] ------
2025-08-26 11:43:26.727|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-26 11:43:29.110|redisson-netty-2-1| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for ***********/***********:6379  [] ------
2025-08-26 11:43:29.916|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for ***********/***********:6379  [] ------
2025-08-26 11:43:44.038|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-26 11:43:46.265|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-26 11:43:46.875|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-26 11:43:48.213|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-26 11:43:48.905|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-26 11:43:48.907|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-26 11:43:49.008|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-26 11:43:50.435|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-26 11:43:56.037|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-26 11:43:56.629|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 35.218 seconds (JVM running for 38.665)  [] ------
2025-08-26 11:43:56.642|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-26 11:43:56.939|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-26 11:43:59.831|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-26 11:44:01.921|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-26 11:44:03.019|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-26 11:44:03.814|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-26 11:44:04.898|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-26 11:44:17.376|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-26 11:44:31.018|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-26 11:49:13.645|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-26 11:49:13.645|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-26 11:49:13.669|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 23 ms  [] ------
2025-08-26 11:49:14.088|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68ad2eba3e764e207dc783cf] ------
2025-08-26 11:49:14.582|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/ProjectContract/QueryBidMapHotelInfo FINISH in -1ms returning {"code":"401","data":null,"message":"Session expired, please login again","successful":false}  [68ad2eba3e764e207dc783cf] ------
2025-08-26 11:49:14.680|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"401","data":null,"message":"Session expired, please login again","successful":false}  [] ------
2025-08-26 11:49:14.760|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [] ------
2025-08-26 11:50:08.297|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68ad2ef03e764e207dc783d0] ------
2025-08-26 11:50:08.467|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"cityCode":null,"hotelGroupBrandIdList":null,"hotelId":163319,"hotelName":null,"orgId":null,"projectId":115,"pageIndex":1,"pageSize":20}  [68ad2ef03e764e207dc783d0] ------
2025-08-26 11:50:12.830|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidHotelInfoQueryResponseList":[],"bidState":1,"bidUnApplicableDayList":[{"endDate":"Mon Feb 03 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Tue Oct 07 00:00:00 CST 2025","startDate":"Wed Oct 01 00:00:00 CST 2025"}],"bidWeight":0,"breakfastNum":"One Breakfast","cityCode":"YJI","cityName":"Yangjiang","cityQueryProjectHotelBidStatResponse":{"lastYearAmount":null,"lastYearAmountOrder":null,"lastYearRoomNightCount":null,"lastYearRoomNightOrder":null,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":1},"cityTheSameLevelQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":0},"currencyCode":"CNY","hotelId":163319,"hotelImageList":null,"hotelName":"Hualuxe Yangjiang City Center","hotelPriceLevelInfoVOList":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":99,"lastOnePersonIncludeTaxPrice":368.78,"lastOnePersonPrice":368.78,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":430.00,"onePersonPrice":368.78,"onePersonPriceTaxInfo":{"basePrice":368.78,"bidPrice":368.78,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":36.88,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":24.34,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":430.00,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":61.22},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":66,"hotelPriceLevelId":84,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":84,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":68,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1}, {"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":100,"lastOnePersonIncludeTaxPrice":427.95,"lastOnePersonPrice":427.95,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":498.99,"onePersonPrice":427.95,"onePersonPriceTaxInfo":{"basePrice":427.95,"bidPrice":427.95,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":42.80,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":28.24,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":498.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":71.04},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":67,"hotelPriceLevelId":85,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":85,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":69,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1}],"hotelProjectPoiInfoResponseList":[],"hotelServicePoint":null,"hotelStar":"29","hotelStarName":"Quisa Five Star","isInvitedHotel":0,"isRecommendHotel":null,"lastYearHotelPriceLevelInfoVOList":null,"lastYearTotalRoomNight":"--","lastYearTotalSalesAmount":"--","latGoogle":21.**********,"latestYearOrder":0,"latestYearRoomNight":0,"lngGoogle":111.**********,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/319\/163319\/201706241498261831801.jpg","minPrice":368.78,"poiDistance":null,"poiName":null,"projectHotelRemarkResponseList":[],"projectIntentHotelId":230,"projectWeight":6.00,"rating":"4.7","relatedProjectId":109,"roomTypeDesc":"Standard","theSameLevelOrder":0,"threeQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":0},"totalRoomNight100":null,"totalRoomNight100To200":null,"totalRoomNight200To300":null,"totalRoomNight300To400":null,"totalRoomNight400To500":null,"totalRoomNight500":null,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1},"message":"Succeeded","successful":true}  [68ad2ef03e764e207dc783d0] ------
2025-08-26 11:50:13.692|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68ad2ef03e764e207dc783d0] ------
2025-08-26 11:50:45.186|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68ad2f153e764e207dc783d1] ------
2025-08-26 11:50:45.235|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"cityCode":null,"hotelGroupBrandIdList":null,"hotelId":163319,"hotelName":null,"orgId":null,"projectId":115,"pageIndex":1,"pageSize":20}  [68ad2f153e764e207dc783d1] ------
2025-08-26 11:50:48.716|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidHotelInfoQueryResponseList":[],"bidState":1,"bidUnApplicableDayList":[{"endDate":"Mon Feb 03 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Tue Oct 07 00:00:00 CST 2025","startDate":"Wed Oct 01 00:00:00 CST 2025"}],"bidWeight":0,"breakfastNum":"One Breakfast","cityCode":"YJI","cityName":"Yangjiang","cityQueryProjectHotelBidStatResponse":{"lastYearAmount":null,"lastYearAmountOrder":null,"lastYearRoomNightCount":null,"lastYearRoomNightOrder":null,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":1},"cityTheSameLevelQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":0},"currencyCode":"CNY","hotelId":163319,"hotelImageList":null,"hotelName":"Hualuxe Yangjiang City Center","hotelPriceLevelInfoVOList":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":99,"lastOnePersonIncludeTaxPrice":368.78,"lastOnePersonPrice":368.78,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":430.00,"onePersonPrice":368.78,"onePersonPriceTaxInfo":{"basePrice":368.78,"bidPrice":368.78,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":36.88,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":24.34,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":430.00,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":61.22},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":66,"hotelPriceLevelId":84,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":84,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":68,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}, {"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":100,"lastOnePersonIncludeTaxPrice":427.95,"lastOnePersonPrice":427.95,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":498.99,"onePersonPrice":427.95,"onePersonPriceTaxInfo":{"basePrice":427.95,"bidPrice":427.95,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":42.80,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":28.24,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":498.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":71.04},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":67,"hotelPriceLevelId":85,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":85,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":69,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}],"hotelProjectPoiInfoResponseList":[],"hotelServicePoint":null,"hotelStar":"29","hotelStarName":"Quisa Five Star","isInvitedHotel":0,"isRecommendHotel":null,"lastYearHotelPriceLevelInfoVOList":null,"lastYearTotalRoomNight":"--","lastYearTotalSalesAmount":"--","latGoogle":21.**********,"latestYearOrder":0,"latestYearRoomNight":0,"lngGoogle":111.**********,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/319\/163319\/201706241498261831801.jpg","minPrice":368.78,"poiDistance":null,"poiName":null,"projectHotelRemarkResponseList":[],"projectIntentHotelId":230,"projectWeight":6.00,"rating":"4.7","relatedProjectId":109,"roomTypeDesc":"Standard","theSameLevelOrder":0,"threeQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":0},"totalRoomNight100":null,"totalRoomNight100To200":null,"totalRoomNight200To300":null,"totalRoomNight300To400":null,"totalRoomNight400To500":null,"totalRoomNight500":null,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********},"message":"Succeeded","successful":true}  [68ad2f153e764e207dc783d1] ------
2025-08-26 11:50:49.359|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68ad2f153e764e207dc783d1] ------
2025-08-26 11:51:58.110|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68ad2f5e3e764e207dc783d2] ------
2025-08-26 11:51:58.244|http-nio-6888-exec-7| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"cityCode":null,"hotelGroupBrandIdList":null,"hotelId":163319,"hotelName":null,"orgId":null,"projectId":115,"pageIndex":1,"pageSize":20}  [68ad2f5e3e764e207dc783d2] ------
2025-08-26 11:52:02.063|http-nio-6888-exec-7| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidHotelInfoQueryResponseList":[],"bidState":1,"bidUnApplicableDayList":[{"endDate":"Mon Feb 03 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Tue Oct 07 00:00:00 CST 2025","startDate":"Wed Oct 01 00:00:00 CST 2025"}],"bidWeight":0,"breakfastNum":"One Breakfast","cityCode":"YJI","cityName":"Yangjiang","cityQueryProjectHotelBidStatResponse":{"lastYearAmount":null,"lastYearAmountOrder":null,"lastYearRoomNightCount":null,"lastYearRoomNightOrder":null,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":1},"cityTheSameLevelQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":0},"currencyCode":"CNY","hotelId":163319,"hotelImageList":null,"hotelName":"Hualuxe Yangjiang City Center","hotelPriceLevelInfoVOList":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":99,"lastOnePersonIncludeTaxPrice":368.78,"lastOnePersonPrice":368.78,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":430.00,"onePersonPrice":368.78,"onePersonPriceTaxInfo":{"basePrice":368.78,"bidPrice":368.78,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":36.88,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":24.34,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":430.00,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":61.22},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":66,"hotelPriceLevelId":84,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":84,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":68,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1}, {"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":100,"lastOnePersonIncludeTaxPrice":427.95,"lastOnePersonPrice":427.95,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":498.99,"onePersonPrice":427.95,"onePersonPriceTaxInfo":{"basePrice":427.95,"bidPrice":427.95,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":42.80,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":28.24,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":498.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":71.04},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":67,"hotelPriceLevelId":85,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":85,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":69,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1}],"hotelProjectPoiInfoResponseList":[],"hotelServicePoint":null,"hotelStar":"29","hotelStarName":"Quisa Five Star","isInvitedHotel":0,"isRecommendHotel":null,"lastYearHotelPriceLevelInfoVOList":null,"lastYearTotalRoomNight":"--","lastYearTotalSalesAmount":"--","latGoogle":21.**********,"latestYearOrder":0,"latestYearRoomNight":0,"lngGoogle":111.**********,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/319\/163319\/201706241498261831801.jpg","minPrice":368.78,"poiDistance":null,"poiName":null,"projectHotelRemarkResponseList":[],"projectIntentHotelId":230,"projectWeight":6.00,"rating":"4.7","relatedProjectId":109,"roomTypeDesc":"Standard","theSameLevelOrder":0,"threeQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.7","ratingOrder":0},"totalRoomNight100":null,"totalRoomNight100To200":null,"totalRoomNight200To300":null,"totalRoomNight300To400":null,"totalRoomNight400To500":null,"totalRoomNight500":null,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1},"message":"Succeeded","successful":true}  [68ad2f5e3e764e207dc783d2] ------
2025-08-26 11:52:03.199|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68ad2f5e3e764e207dc783d2] ------
2025-08-26 11:53:50.257|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68ad2fce3e764e207dc783d3] ------
2025-08-26 11:53:50.309|http-nio-6888-exec-9| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":null,"projectIntentHotelId":230}  [68ad2fce3e764e207dc783d3] ------
2025-08-26 11:53:50.543|http-nio-6888-exec-9| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/ProjectContract/QueryBidHotelPriceLevelList FINISH in -1ms returning {"code":"400","data":null,"message":"[Parameter cannot be null]","successful":false}  [68ad2fce3e764e207dc783d3] ------
2025-08-26 11:53:50.544|http-nio-6888-exec-9| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"400","data":null,"message":"[Parameter cannot be null]","successful":false}  [] ------
2025-08-26 11:53:51.174|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [] ------
2025-08-26 11:54:11.831|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68ad2fe33e764e207dc783d4] ------
2025-08-26 11:54:11.882|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":163319,"projectIntentHotelId":230}  [68ad2fe33e764e207dc783d4] ------
2025-08-26 11:54:13.751|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":99,"lastOnePersonIncludeTaxPrice":368.78,"lastOnePersonPrice":368.78,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":430.00,"onePersonPrice":368.78,"onePersonPriceTaxInfo":{"basePrice":368.78,"bidPrice":368.78,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":36.88,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":24.34,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":430.00,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":61.22},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":66,"hotelPriceLevelId":84,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":84,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":68,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1}, {"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":100,"lastOnePersonIncludeTaxPrice":427.95,"lastOnePersonPrice":427.95,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":498.99,"onePersonPrice":427.95,"onePersonPriceTaxInfo":{"basePrice":427.95,"bidPrice":427.95,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":42.80,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":28.24,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":498.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":71.04},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":67,"hotelPriceLevelId":85,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":85,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":69,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1}],"message":"Succeeded","successful":true}  [68ad2fe33e764e207dc783d4] ------
2025-08-26 11:54:14.514|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68ad2fe33e764e207dc783d4] ------
2025-08-26 11:54:24.726|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68ad2ff03e764e207dc783d5] ------
2025-08-26 11:54:24.779|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":163319,"projectIntentHotelId":230}  [68ad2ff03e764e207dc783d5] ------
2025-08-26 11:54:26.326|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":99,"lastOnePersonIncludeTaxPrice":368.78,"lastOnePersonPrice":368.78,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":430.00,"onePersonPrice":368.78,"onePersonPriceTaxInfo":{"basePrice":368.78,"bidPrice":368.78,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":36.88,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":24.34,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":430.00,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":61.22},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":66,"hotelPriceLevelId":84,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":84,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":68,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}, {"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":100,"lastOnePersonIncludeTaxPrice":427.95,"lastOnePersonPrice":427.95,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":498.99,"onePersonPrice":427.95,"onePersonPriceTaxInfo":{"basePrice":427.95,"bidPrice":427.95,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":42.80,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":28.24,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":498.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":71.04},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":67,"hotelPriceLevelId":85,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":85,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":69,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}],"message":"Succeeded","successful":true}  [68ad2ff03e764e207dc783d5] ------
2025-08-26 11:54:26.995|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68ad2ff03e764e207dc783d5] ------
2025-08-26 11:54:40.894|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68ad30003e764e207dc783d6] ------
2025-08-26 11:54:40.944|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":163319,"projectIntentHotelId":230}  [68ad30003e764e207dc783d6] ------
2025-08-26 11:54:42.488|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":99,"lastOnePersonIncludeTaxPrice":368.78,"lastOnePersonPrice":368.78,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":430.00,"onePersonPrice":368.78,"onePersonPriceTaxInfo":{"basePrice":368.78,"bidPrice":368.78,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":36.88,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":24.34,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":430.00,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":61.22},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":66,"hotelPriceLevelId":84,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":84,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":68,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1}, {"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":100,"lastOnePersonIncludeTaxPrice":427.95,"lastOnePersonPrice":427.95,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":498.99,"onePersonPrice":427.95,"onePersonPriceTaxInfo":{"basePrice":427.95,"bidPrice":427.95,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":42.80,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":28.24,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":498.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":71.04},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":67,"hotelPriceLevelId":85,"isIncludeBreakfast":1,"isLocked":null,"lra":1,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":85,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":69,"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1}],"message":"Succeeded","successful":true}  [68ad30003e764e207dc783d6] ------
2025-08-26 11:54:43.293|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68ad30003e764e207dc783d6] ------
2025-08-26 11:55:26.986|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68ad302e3e764e207dc783d7] ------
2025-08-26 11:55:27.100|http-nio-6888-exec-7| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":null,"projectId":null,"projectIntentHotelId":230}  [68ad302e3e764e207dc783d7] ------
2025-08-26 11:55:29.373|http-nio-6888-exec-7| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [68ad302e3e764e207dc783d7] ------
2025-08-26 11:55:31.261|http-nio-6888-exec-7| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelImage, Request Body: {"businessRequest":{"hotelIds":[163319]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelImage","signature":"FBF9F8AB6A095E52167C7666DD5E519A","timestamp":1756180530299,"version":"1.0.0"}}  [68ad302e3e764e207dc783d7] ------
2025-08-26 11:55:33.054|http-nio-6888-exec-7| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidCustomStrategyList":[],"bidHotelPriceLevelInfoVOList":null,"bidHotelTaxSettings":{"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"bidOperateLogList":[{"bidOperateLogId":118,"hotelId":163319,"operateContent":"BID_STATUS_UPDATE_TO_NEW_BID","operateTime":"Wed Aug 13 00:51:55 CST 2025","operator":"chen","orgTypeId":4,"projectId":115,"projectIntentHotelId":230}],"bidProjectStrategy":{"commission":null,"doAfterLateReserveTime":null,"hasCommission":null,"isIncludeBreakfast":null,"lateReserveTime":null,"supportCancelDay":0,"supportCancelTime":"","supportCheckinInfo":null,"supportIncludeTaxService":null,"supportNoGuarantee":null,"supportPayAtHotel":null,"supportPayEarlyCheckout":null,"supportVccPay":null,"supportWifi":1},"bidUnApplicableDayList":[{"endDate":"Mon Feb 03 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Tue Oct 07 00:00:00 CST 2025","startDate":"Wed Oct 01 00:00:00 CST 2025"}],"currencyCode":"CNY","hotelId":163319,"hotelImageList":[Image(imageId=1782266, imageType=5, isMain=1, url=http://fcimage.fangcang.com/images/hotels/319/163319/201706241498261831801.jpg), Image(imageId=*********, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/2008060000001hilm71DF_R_550_412_R5.jpg), Image(imageId=*********, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/200a060000001hi4v16AD_R_550_412_R5.jpg), Image(imageId=*********, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/2002060000001hi50C528_R_550_412_R5.jpg), Image(imageId=*********, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/200m060000001hi5170A6_R_550_412_R5.jpg), Image(imageId=100989137, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g2/M01/DC/23/Cghzf1USXc-AZ3cLAEDo146leNM658_R_550_412_R5.jpg), Image(imageId=100989138, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/200b060000001hi531147_R_550_412_R5.jpg), Image(imageId=100989139, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/200r060000001hi545531_R_550_412_R5.jpg), Image(imageId=100989148, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g4/M07/65/C3/CggYHVXmb_SAHRYhAARmMZK56m4885_R_550_412_R5.jpg), Image(imageId=100989149, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g3/M0A/63/54/CggYGVXmb_WAVJZPAAWaI0TO0W8648_R_550_412_R5.jpg), Image(imageId=100989150, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g4/M00/65/95/CggYHFXmb_-AcM6bAAUzvS05em0637_R_550_412_R5.jpg), Image(imageId=100989151, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g3/M0B/1A/83/CggYGVX2eHOASV8JADpSIrWjJxM054_R_550_412_R5.jpg), Image(imageId=100989144, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/2006060000001hi4uA4EC_R_550_412_R5.jpg), Image(imageId=100989145, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g1/M0A/CC/6B/CghzfFURIAyAD_CRADV2NOI9SZ0022_R_550_412_R5.jpg), Image(imageId=100989146, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g1/M08/CC/6B/CghzfFURIAyAfwWWADlJ5w8GrfQ780_R_550_412_R5.jpg), Image(imageId=100989147, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/20030k000000b6tj2BB2E_R_550_412_R5.jpg), Image(imageId=100989156, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//200e13000000u1irsDD3A_R_550_412_R5.jpg), Image(imageId=100989157, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD5c120008vsii968EAD_R_550_412_R5.jpg), Image(imageId=100989158, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//2002060000001hi50C528_R_550_412_R5.jpg), Image(imageId=100989159, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//200a060000001hi4v16AD_R_550_412_R5.jpg), Image(imageId=100989152, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/200r060000001hi545531_R_550_412_R5.jpg), Image(imageId=100989153, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//fd/hotel/g2/M01/DC/23/Cghzf1USXc-AZ3cLAEDo146leNM658_R_550_412_R5.jpg), Image(imageId=100989154, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD2v120008vrnmynE234_R_550_412_R5.jpg), Image(imageId=100989155, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD3g120008vsib4f9033_R_550_412_R5.jpg), Image(imageId=100989164, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD5a120008vsig9dD0EA_R_550_412_R5.jpg), Image(imageId=100989165, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD3t120008wpv3p56AD4_R_550_412_R5.jpg), Image(imageId=100989166, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//fd/hotel/g1/M0A/CC/6B/CghzfFURIAyAD_CRADV2NOI9SZ0022_R_550_412_R5.jpg), Image(imageId=100989167, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images//20011e000001f8mb8C488_R_550_412_R5.jpg), Image(imageId=100989160, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD53120008vsie6n8384_R_550_412_R5.jpg), Image(imageId=100989161, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD32120008vsibh8DAB0_R_550_412_R5.jpg), Image(imageId=100989162, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//2006060000001hi4uA4EC_R_550_412_R5.jpg), Image(imageId=100989163, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//200m060000001hi5170A6_R_550_412_R5.jpg), Image(imageId=100989172, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//fd/hotel/g4/M07/65/C3/CggYHVXmb_SAHRYhAARmMZK56m4885_R_550_412_R5.jpg), Image(imageId=100989173, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD4p120008vsij8j5730_R_550_412_R5.jpg), Image(imageId=100989174, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD2v120008vrnmynE234_R_550_412_R5.jpg), Image(imageId=100989175, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//1mc6n12000cqncrdjAC7D_R_550_412_R5.jpg), Image(imageId=100989168, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD32120008vsi5suC625_R_550_412_R5.jpg), Image(imageId=100989169, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0225s12000axk9gqgE964_R_550_412_R5.jpg), Image(imageId=100989170, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0225012000axk9ks9FF8B_R_550_412_R5.jpg), Image(imageId=100989171, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD0d120008vsibgxF382_R_550_412_R5.jpg), Image(imageId=100989176, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//1mc5w12000cqnctspAF9E_R_550_412_R5.jpg), Image(imageId=100989177, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images//1mc3h12000cqn8jh54F3B_R_550_412_R5.jpg), Image(imageId=100989178, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0226a12000duf00whC211_R_550_412_R5.jpg), Image(imageId=100989179, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0586m12000eslez13690A_R_550_412_R5.jpg)],"hotelInfo":{"cityCode":"YJI","cityName":null,"countryCode":"CN","countryName":null,"currencyCode":"CNY","fitmentDate":"Mon Jun 01 00:00:00 CST 2015","hotelAddress":"No. 8 Huanhu Road","hotelBrandId":326139,"hotelBrandName":"HUALUXE","hotelGroupId":30060,"hotelId":163319,"hotelName":"Hualuxe Yangjiang City Center","hotelStar":"29","hotelStarName":"Quisa Five Star","isActive":1,"latGoogle":21.**********,"lngGoogle":111.**********,"openingDate":"Sun Feb 01 00:00:00 CST 2015","provinceCode":"GDN","provinceName":null,"rating":"4.7","roomCount":281,"telephone":"+86-662-8333333"},"isLanyonImport":1,"lanyonImportDataCount":0,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/319\/163319\/201706241498261831801.jpg","poiDistance":null,"poiName":null,"projectCustomTendStrategyList":[{"displayOrder":1,"id":116,"options":null,"strategyName":"\u81EA\u5B9A\u4E49\u7B56\u75651","strategyType":1,"supportStrategyName":1}, {"displayOrder":2,"id":117,"options":null,"strategyName":"\u81EA\u5B9A\u4E49\u7B56\u75652","strategyType":1,"supportStrategyName":1}, {"displayOrder":3,"id":118,"options":null,"strategyName":"\u81EA\u5B9A\u4E49\u6587\u672C1","strategyType":2,"supportStrategyName":0}, {"displayOrder":4,"id":154,"options":[{"displayOrder":1,"id":51,"optionName":"AA","strategyId":154,"weightScore":null}, {"displayOrder":2,"id":52,"optionName":"BB","strategyId":154,"weightScore":null}, {"displayOrder":3,"id":53,"optionName":"CCC","strategyId":154,"weightScore":null}],"strategyName":"\u591A\u9009\u9879\u56DE\u590D1","strategyType":3,"supportStrategyName":0}, {"displayOrder":5,"id":155,"options":[{"displayOrder":1,"id":54,"optionName":"A","strategyId":155,"weightScore":null}, {"displayOrder":2,"id":55,"optionName":"B","strategyId":155,"weightScore":null}, {"displayOrder":3,"id":56,"optionName":"C","strategyId":155,"weightScore":null}],"strategyName":"\u5355\u9009\u9879\u56DE\u590D","strategyType":4,"supportStrategyName":0}],"projectHotelRemarkResponseList":[],"projectHotelTendStrategy":{"createTime":"Mon Jul 21 11:34:46 CST 2025","creator":"gang","id":136,"isRecommendIncludeAllTaxes":0,"isRecommendNoBreakfastDouble":0,"isRecommendNoBreakfastSingle":0,"isRecommendWithBreakfastDouble":0,"isRecommendWithBreakfastSingle":0,"isRequireIncludeAllTaxes":0,"isRequireNoBreakfastDouble":0,"isRequireNoBreakfastSingle":0,"isRequireTaxDetails":0,"isRequireWithBreakfastDouble":0,"isRequireWithBreakfastSingle":0,"limitMaxPrice":1000.00,"limitMinPrice":10.00,"limitPriceCurrencyCode":"USD","maxNotApplicableDay":10,"maxRoomTypeCount":2,"maxSeasonDay":100,"modifier":"\u738B\u5CF0","modifyTime":"Wed Aug 06 00:06:58 CST 2025","projectId":115,"supportCancel":1,"supportCancelDay":1,"supportCancelTime":"17:00","supportCheckinInfo":1,"supportIncludeTaxService":1,"supportLra":1,"supportMaxNotApplicableDay":1,"supportMaxRoomTypeCount":1,"supportNoGuarantee":1,"supportPayAtHotel":1,"supportPayEarlyCheckout":1,"supportPriceLimit":true,"supportSeasonDayLimit":1,"supportVccPay":0,"supportWifi":1},"projectId":115,"projectIntentHotelBid":{"bidOrgId":134,"bidOrgType":4,"bidState":1,"bidUploadSource":2,"bidWeight":null,"employRight":null,"employRightFileUrl":null,"hotelBidContactEmail":"<EMAIL>","hotelBidContactMobile":"8333333","hotelBidContactName":"Heita Wu","hotelGroupBidContactEmail":"<EMAIL>","hotelGroupBidContactMobile":"*********","hotelGroupBidContactName":"chen","hotelId":163319,"hotelServicePoints":100.00,"inviteStatus":0,"isUpload":1,"lastYearRoomNight":null,"projectId":115,"projectIntentHotelId":230,"sendMailStatus":0,"tenderAvgPrice":null},"roomNameList":[{"hotelId":null,"roomId":6462946,"roomName":"Superior Lakeview Twin Room"}, {"hotelId":null,"roomId":6462947,"roomName":"Deluxe Lakeview Big bed Room"}, {"hotelId":null,"roomId":3562039,"roomName":"Club Room"}, {"hotelId":null,"roomId":6462945,"roomName":"Standard Double room with lake view twin beds"}, {"hotelId":null,"roomId":6462948,"roomName":"Superior Lakeview Big bed Room"}, {"hotelId":null,"roomId":6462949,"roomName":"One bedroom Lakeview Suite"}, {"hotelId":null,"roomId":10759470,"roomName":"Executive Room (Double Bed)"}, {"hotelId":null,"roomId":3570492,"roomName":"Superior Room, 1 King Bed"}, {"hotelId":null,"roomId":6462958,"roomName":"Deluxe executive twin room"}, {"hotelId":null,"roomId":10759478,"roomName":"Suite - 1-Bedroom (King Bed)"}, {"hotelId":null,"roomId":10759479,"roomName":"Suite - 1-Bedroom (King Bed)"}, {"hotelId":null,"roomId":10759474,"roomName":"Executive Room (2 Beds)"}, {"hotelId":null,"roomId":8714926,"roomName":"One bedroom suite with an oversized bed on the upper floor"}, {"hotelId":null,"roomId":7498434,"roomName":"2 Double Standard Lake View"}, {"hotelId":null,"roomId":7498435,"roomName":"2 Double Premium Lounge Access"}, {"hotelId":null,"roomId":7498433,"roomName":"1 King Standard Lake View"}, {"hotelId":null,"roomId":3494657,"roomName":"Premium Room"}, {"hotelId":null,"roomId":8714929,"roomName":"One-bedroom suite with one king bed"}, {"hotelId":null,"roomId":7498436,"roomName":"1 King Premium Lounge Access"}, {"hotelId":null,"roomId":461644,"roomName":"Huayi Deluxe Room"}, {"hotelId":null,"roomId":461643,"roomName":"Standard Room"}, {"hotelId":null,"roomId":1455686,"roomName":"Superior Queen Room"}, {"hotelId":null,"roomId":1455687,"roomName":"Deluxe Queen Room"}, {"hotelId":null,"roomId":1455682,"roomName":"Superior twin room"}, {"hotelId":null,"roomId":1455683,"roomName":"Deluxe twin room"}, {"hotelId":null,"roomId":4600484,"roomName":"2 Double Standard"}, {"hotelId":null,"roomId":4600482,"roomName":"Executive Double room full double bed"}, {"hotelId":null,"roomId":3566290,"roomName":"Deluxe Suite"}, {"hotelId":null,"roomId":4600483,"roomName":"1 King Standard"}, {"hotelId":null,"roomId":3566288,"roomName":"Deluxe Room"}, {"hotelId":null,"roomId":3566289,"roomName":"Superior Room, 2 Double Beds"}],"viewCurrencyCode":"CNY","viewCurrencyExchangeRate":1},"message":"Succeeded","successful":true}  [68ad302e3e764e207dc783d7] ------
2025-08-26 11:55:34.316|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68ad302e3e764e207dc783d7] ------
2025-08-26 11:55:43.368|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68ad303f3e764e207dc783d8] ------
2025-08-26 11:55:43.477|http-nio-6888-exec-9| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":null,"projectId":null,"projectIntentHotelId":230}  [68ad303f3e764e207dc783d8] ------
2025-08-26 11:55:45.362|http-nio-6888-exec-9| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelImage, Request Body: {"businessRequest":{"hotelIds":[163319]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelImage","signature":"2C3F6EE010E98B6B98C200232509A6F5","timestamp":1756180545157,"version":"1.0.0"}}  [68ad303f3e764e207dc783d8] ------
2025-08-26 11:55:47.213|http-nio-6888-exec-9| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidCustomStrategyList":[],"bidHotelPriceLevelInfoVOList":null,"bidHotelTaxSettings":{"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"bidOperateLogList":[{"bidOperateLogId":118,"hotelId":163319,"operateContent":"BID_STATUS_UPDATE_TO_NEW_BID","operateTime":"Wed Aug 13 00:51:55 CST 2025","operator":"chen","orgTypeId":4,"projectId":115,"projectIntentHotelId":230}],"bidProjectStrategy":{"commission":null,"doAfterLateReserveTime":null,"hasCommission":null,"isIncludeBreakfast":null,"lateReserveTime":null,"supportCancelDay":0,"supportCancelTime":"","supportCheckinInfo":null,"supportIncludeTaxService":null,"supportNoGuarantee":null,"supportPayAtHotel":null,"supportPayEarlyCheckout":null,"supportVccPay":null,"supportWifi":1},"bidUnApplicableDayList":[{"endDate":"Mon Feb 03 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Tue Oct 07 00:00:00 CST 2025","startDate":"Wed Oct 01 00:00:00 CST 2025"}],"currencyCode":"CNY","hotelId":163319,"hotelImageList":[Image(imageId=1782266, imageType=5, isMain=1, url=http://fcimage.fangcang.com/images/hotels/319/163319/201706241498261831801.jpg), Image(imageId=*********, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/2008060000001hilm71DF_R_550_412_R5.jpg), Image(imageId=*********, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/200a060000001hi4v16AD_R_550_412_R5.jpg), Image(imageId=*********, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/2002060000001hi50C528_R_550_412_R5.jpg), Image(imageId=*********, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/200m060000001hi5170A6_R_550_412_R5.jpg), Image(imageId=100989137, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g2/M01/DC/23/Cghzf1USXc-AZ3cLAEDo146leNM658_R_550_412_R5.jpg), Image(imageId=100989138, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/200b060000001hi531147_R_550_412_R5.jpg), Image(imageId=100989139, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/200r060000001hi545531_R_550_412_R5.jpg), Image(imageId=100989148, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g4/M07/65/C3/CggYHVXmb_SAHRYhAARmMZK56m4885_R_550_412_R5.jpg), Image(imageId=100989149, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g3/M0A/63/54/CggYGVXmb_WAVJZPAAWaI0TO0W8648_R_550_412_R5.jpg), Image(imageId=100989150, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g4/M00/65/95/CggYHFXmb_-AcM6bAAUzvS05em0637_R_550_412_R5.jpg), Image(imageId=100989151, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g3/M0B/1A/83/CggYGVX2eHOASV8JADpSIrWjJxM054_R_550_412_R5.jpg), Image(imageId=100989144, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/2006060000001hi4uA4EC_R_550_412_R5.jpg), Image(imageId=100989145, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g1/M0A/CC/6B/CghzfFURIAyAD_CRADV2NOI9SZ0022_R_550_412_R5.jpg), Image(imageId=100989146, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/fd/hotel/g1/M08/CC/6B/CghzfFURIAyAfwWWADlJ5w8GrfQ780_R_550_412_R5.jpg), Image(imageId=100989147, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images/20030k000000b6tj2BB2E_R_550_412_R5.jpg), Image(imageId=100989156, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//200e13000000u1irsDD3A_R_550_412_R5.jpg), Image(imageId=100989157, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD5c120008vsii968EAD_R_550_412_R5.jpg), Image(imageId=100989158, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//2002060000001hi50C528_R_550_412_R5.jpg), Image(imageId=100989159, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//200a060000001hi4v16AD_R_550_412_R5.jpg), Image(imageId=100989152, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images/200r060000001hi545531_R_550_412_R5.jpg), Image(imageId=100989153, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//fd/hotel/g2/M01/DC/23/Cghzf1USXc-AZ3cLAEDo146leNM658_R_550_412_R5.jpg), Image(imageId=100989154, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD2v120008vrnmynE234_R_550_412_R5.jpg), Image(imageId=100989155, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD3g120008vsib4f9033_R_550_412_R5.jpg), Image(imageId=100989164, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD5a120008vsig9dD0EA_R_550_412_R5.jpg), Image(imageId=100989165, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD3t120008wpv3p56AD4_R_550_412_R5.jpg), Image(imageId=100989166, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//fd/hotel/g1/M0A/CC/6B/CghzfFURIAyAD_CRADV2NOI9SZ0022_R_550_412_R5.jpg), Image(imageId=100989167, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images//20011e000001f8mb8C488_R_550_412_R5.jpg), Image(imageId=100989160, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD53120008vsie6n8384_R_550_412_R5.jpg), Image(imageId=100989161, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD32120008vsibh8DAB0_R_550_412_R5.jpg), Image(imageId=100989162, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//2006060000001hi4uA4EC_R_550_412_R5.jpg), Image(imageId=100989163, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//200m060000001hi5170A6_R_550_412_R5.jpg), Image(imageId=100989172, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//fd/hotel/g4/M07/65/C3/CggYHVXmb_SAHRYhAARmMZK56m4885_R_550_412_R5.jpg), Image(imageId=100989173, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD4p120008vsij8j5730_R_550_412_R5.jpg), Image(imageId=100989174, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD2v120008vrnmynE234_R_550_412_R5.jpg), Image(imageId=100989175, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//1mc6n12000cqncrdjAC7D_R_550_412_R5.jpg), Image(imageId=100989168, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD32120008vsi5suC625_R_550_412_R5.jpg), Image(imageId=100989169, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0225s12000axk9gqgE964_R_550_412_R5.jpg), Image(imageId=100989170, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0225012000axk9ks9FF8B_R_550_412_R5.jpg), Image(imageId=100989171, imageType=5, isMain=0, url=http://dimg04.c-ctrip.com/images//0AD0d120008vsibgxF382_R_550_412_R5.jpg), Image(imageId=100989176, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//1mc5w12000cqnctspAF9E_R_550_412_R5.jpg), Image(imageId=100989177, imageType=4, isMain=0, url=http://dimg04.c-ctrip.com/images//1mc3h12000cqn8jh54F3B_R_550_412_R5.jpg), Image(imageId=100989178, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0226a12000duf00whC211_R_550_412_R5.jpg), Image(imageId=100989179, imageType=2, isMain=0, url=http://dimg04.c-ctrip.com/images//0586m12000eslez13690A_R_550_412_R5.jpg)],"hotelInfo":{"cityCode":"YJI","cityName":null,"countryCode":"CN","countryName":null,"currencyCode":"CNY","fitmentDate":"Mon Jun 01 00:00:00 CST 2015","hotelAddress":"No. 8 Huanhu Road","hotelBrandId":326139,"hotelBrandName":"HUALUXE","hotelGroupId":30060,"hotelId":163319,"hotelName":"Hualuxe Yangjiang City Center","hotelStar":"29","hotelStarName":"Quisa Five Star","isActive":1,"latGoogle":21.**********,"lngGoogle":111.**********,"openingDate":"Sun Feb 01 00:00:00 CST 2015","provinceCode":"GDN","provinceName":null,"rating":"4.7","roomCount":281,"telephone":"+86-662-8333333"},"isLanyonImport":1,"lanyonImportDataCount":0,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/319\/163319\/201706241498261831801.jpg","poiDistance":null,"poiName":null,"projectCustomTendStrategyList":[{"displayOrder":1,"id":116,"options":null,"strategyName":"\u81EA\u5B9A\u4E49\u7B56\u75651","strategyType":1,"supportStrategyName":1}, {"displayOrder":2,"id":117,"options":null,"strategyName":"\u81EA\u5B9A\u4E49\u7B56\u75652","strategyType":1,"supportStrategyName":1}, {"displayOrder":3,"id":118,"options":null,"strategyName":"\u81EA\u5B9A\u4E49\u6587\u672C1","strategyType":2,"supportStrategyName":0}, {"displayOrder":4,"id":154,"options":[{"displayOrder":1,"id":51,"optionName":"AA","strategyId":154,"weightScore":null}, {"displayOrder":2,"id":52,"optionName":"BB","strategyId":154,"weightScore":null}, {"displayOrder":3,"id":53,"optionName":"CCC","strategyId":154,"weightScore":null}],"strategyName":"\u591A\u9009\u9879\u56DE\u590D1","strategyType":3,"supportStrategyName":0}, {"displayOrder":5,"id":155,"options":[{"displayOrder":1,"id":54,"optionName":"A","strategyId":155,"weightScore":null}, {"displayOrder":2,"id":55,"optionName":"B","strategyId":155,"weightScore":null}, {"displayOrder":3,"id":56,"optionName":"C","strategyId":155,"weightScore":null}],"strategyName":"\u5355\u9009\u9879\u56DE\u590D","strategyType":4,"supportStrategyName":0}],"projectHotelRemarkResponseList":[],"projectHotelTendStrategy":{"createTime":"Mon Jul 21 11:34:46 CST 2025","creator":"gang","id":136,"isRecommendIncludeAllTaxes":0,"isRecommendNoBreakfastDouble":0,"isRecommendNoBreakfastSingle":0,"isRecommendWithBreakfastDouble":0,"isRecommendWithBreakfastSingle":0,"isRequireIncludeAllTaxes":0,"isRequireNoBreakfastDouble":0,"isRequireNoBreakfastSingle":0,"isRequireTaxDetails":0,"isRequireWithBreakfastDouble":0,"isRequireWithBreakfastSingle":0,"limitMaxPrice":1000.00,"limitMinPrice":10.00,"limitPriceCurrencyCode":"USD","maxNotApplicableDay":10,"maxRoomTypeCount":2,"maxSeasonDay":100,"modifier":"\u738B\u5CF0","modifyTime":"Wed Aug 06 00:06:58 CST 2025","projectId":115,"supportCancel":1,"supportCancelDay":1,"supportCancelTime":"17:00","supportCheckinInfo":1,"supportIncludeTaxService":1,"supportLra":1,"supportMaxNotApplicableDay":1,"supportMaxRoomTypeCount":1,"supportNoGuarantee":1,"supportPayAtHotel":1,"supportPayEarlyCheckout":1,"supportPriceLimit":true,"supportSeasonDayLimit":1,"supportVccPay":0,"supportWifi":1},"projectId":115,"projectIntentHotelBid":{"bidOrgId":134,"bidOrgType":4,"bidState":1,"bidUploadSource":2,"bidWeight":null,"employRight":null,"employRightFileUrl":null,"hotelBidContactEmail":"<EMAIL>","hotelBidContactMobile":"8333333","hotelBidContactName":"Heita Wu","hotelGroupBidContactEmail":"<EMAIL>","hotelGroupBidContactMobile":"*********","hotelGroupBidContactName":"chen","hotelId":163319,"hotelServicePoints":100.00,"inviteStatus":0,"isUpload":1,"lastYearRoomNight":null,"projectId":115,"projectIntentHotelId":230,"sendMailStatus":0,"tenderAvgPrice":null},"roomNameList":[{"hotelId":null,"roomId":6462946,"roomName":"Superior Lakeview Twin Room"}, {"hotelId":null,"roomId":6462947,"roomName":"Deluxe Lakeview Big bed Room"}, {"hotelId":null,"roomId":3562039,"roomName":"Club Room"}, {"hotelId":null,"roomId":6462945,"roomName":"Standard Double room with lake view twin beds"}, {"hotelId":null,"roomId":6462948,"roomName":"Superior Lakeview Big bed Room"}, {"hotelId":null,"roomId":6462949,"roomName":"One bedroom Lakeview Suite"}, {"hotelId":null,"roomId":10759470,"roomName":"Executive Room (Double Bed)"}, {"hotelId":null,"roomId":3570492,"roomName":"Superior Room, 1 King Bed"}, {"hotelId":null,"roomId":6462958,"roomName":"Deluxe executive twin room"}, {"hotelId":null,"roomId":10759478,"roomName":"Suite - 1-Bedroom (King Bed)"}, {"hotelId":null,"roomId":10759479,"roomName":"Suite - 1-Bedroom (King Bed)"}, {"hotelId":null,"roomId":10759474,"roomName":"Executive Room (2 Beds)"}, {"hotelId":null,"roomId":8714926,"roomName":"One bedroom suite with an oversized bed on the upper floor"}, {"hotelId":null,"roomId":7498434,"roomName":"2 Double Standard Lake View"}, {"hotelId":null,"roomId":7498435,"roomName":"2 Double Premium Lounge Access"}, {"hotelId":null,"roomId":7498433,"roomName":"1 King Standard Lake View"}, {"hotelId":null,"roomId":3494657,"roomName":"Premium Room"}, {"hotelId":null,"roomId":8714929,"roomName":"One-bedroom suite with one king bed"}, {"hotelId":null,"roomId":7498436,"roomName":"1 King Premium Lounge Access"}, {"hotelId":null,"roomId":461644,"roomName":"Huayi Deluxe Room"}, {"hotelId":null,"roomId":461643,"roomName":"Standard Room"}, {"hotelId":null,"roomId":1455686,"roomName":"Superior Queen Room"}, {"hotelId":null,"roomId":1455687,"roomName":"Deluxe Queen Room"}, {"hotelId":null,"roomId":1455682,"roomName":"Superior twin room"}, {"hotelId":null,"roomId":1455683,"roomName":"Deluxe twin room"}, {"hotelId":null,"roomId":4600484,"roomName":"2 Double Standard"}, {"hotelId":null,"roomId":4600482,"roomName":"Executive Double room full double bed"}, {"hotelId":null,"roomId":3566290,"roomName":"Deluxe Suite"}, {"hotelId":null,"roomId":4600483,"roomName":"1 King Standard"}, {"hotelId":null,"roomId":3566288,"roomName":"Deluxe Room"}, {"hotelId":null,"roomId":3566289,"roomName":"Superior Room, 2 Double Beds"}],"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********},"message":"Succeeded","successful":true}  [68ad303f3e764e207dc783d8] ------
2025-08-26 11:55:48.263|http-nio-6888-exec-9| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68ad303f3e764e207dc783d8] ------
2025-08-26 11:57:07.190|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68ad30933e764e207dc783d9] ------
2025-08-26 11:57:07.238|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":null,"projectId":null,"projectIntentHotelId":1472}  [68ad30933e764e207dc783d9] ------
2025-08-26 11:57:08.949|http-nio-6888-exec-1| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelImage, Request Body: {"businessRequest":{"hotelIds":[144084]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelImage","signature":"181EBF9CA907401F1B8DA9AAC1A9E2BF","timestamp":1756180628817,"version":"1.0.0"}}  [68ad30933e764e207dc783d9] ------
2025-08-26 11:57:10.298|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sun Aug 31 00:00:00 CST 2025","priceType":1,"startDate":"Fri Aug 01 00:00:00 CST 2025"}],"bidCustomStrategyList":[{"customTendStrategyId":142,"options":[{"isSupport":0,"optionId":18,"optionName":"A"}, {"isSupport":0,"optionId":19,"optionName":"B"}, {"isSupport":0,"optionId":20,"optionName":"C"}],"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u591A\u9009","strategyType":3,"supportStrategyName":null,"supportStrategyText":null}, {"customTendStrategyId":143,"options":null,"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u6587\u672C","strategyType":2,"supportStrategyName":null,"supportStrategyText":null}, {"customTendStrategyId":144,"options":null,"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u662F\u5426","strategyType":1,"supportStrategyName":0,"supportStrategyText":null}, {"customTendStrategyId":145,"options":[{"isSupport":0,"optionId":21,"optionName":"\u5355\u9009\u9879\u56DE\u590D"}],"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u5355\u9009\u9879\u56DE\u590D","strategyType":4,"supportStrategyName":null,"supportStrategyText":null}, {"customTendStrategyId":146,"options":[{"isSupport":0,"optionId":22,"optionName":"\u5355\u9009\u9879\u56DE\u590D1"}, {"isSupport":0,"optionId":23,"optionName":"\u5355\u9009\u9879\u56DE\u590D2"}, {"isSupport":0,"optionId":24,"optionName":"\u5355\u9009\u9879\u56DE\u590D3"}],"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u5355\u9009\u9879\u56DE\u590Dxxx","strategyType":4,"supportStrategyName":null,"supportStrategyText":null}],"bidHotelPriceLevelInfoVOList":null,"bidHotelTaxSettings":null,"bidOperateLogList":null,"bidProjectStrategy":{"commission":null,"doAfterLateReserveTime":null,"hasCommission":null,"isIncludeBreakfast":null,"lateReserveTime":null,"supportCancelDay":0,"supportCancelTime":"18:00","supportCheckinInfo":null,"supportIncludeTaxService":null,"supportNoGuarantee":null,"supportPayAtHotel":null,"supportPayEarlyCheckout":null,"supportVccPay":null,"supportWifi":0},"bidUnApplicableDayList":null,"currencyCode":"USD","hotelId":144084,"hotelImageList":[Image(imageId=1533406, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245111112.jpg), Image(imageId=1533446, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245106968.jpg), Image(imageId=1533442, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245039217.jpg), Image(imageId=1533440, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245113523.jpg), Image(imageId=1533430, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245037920.jpg), Image(imageId=1533428, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245037479.jpg), Image(imageId=1533426, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245113221.jpg), Image(imageId=1533424, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245112960.jpg), Image(imageId=1533438, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245038909.jpg), Image(imageId=1533436, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245038663.jpg), Image(imageId=1533434, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245106654.jpg), Image(imageId=1533432, imageType=4, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245038337.jpg), Image(imageId=13908198, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201801111515641949761.jpg), Image(imageId=1533410, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245111743.jpg), Image(imageId=13908195, imageType=2, isMain=1, url=http://fcimage.fangcang.com/images/hotels/84/144084/201801111515641943619.jpg), Image(imageId=1533408, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245111365.jpg), Image(imageId=13908205, imageType=4, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201801111515641963089.jpg), Image(imageId=1533422, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245106231.jpg), Image(imageId=1533418, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245112289.jpg), Image(imageId=13908202, imageType=4, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201801111515641957849.jpg), Image(imageId=1533416, imageType=3, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245111994.jpg)],"hotelInfo":{"cityCode":"MAC","cityName":null,"countryCode":"CN","countryName":null,"currencyCode":"MOP","fitmentDate":"Sat Dec 01 00:00:00 CST 2018","hotelAddress":"No.82-86 Rua de Pequim, Macau","hotelBrandId":10342,"hotelBrandName":"Holiday Inn","hotelGroupId":30060,"hotelId":144084,"hotelName":"Holiday Inn Macau","hotelStar":"49","hotelStarName":"Four-Star Equivalent","isActive":1,"latGoogle":22.**********,"lngGoogle":113.**********,"openingDate":"Fri Jan 01 00:00:00 CST 1993","provinceCode":"MAC","provinceName":null,"rating":"4.1","roomCount":318,"telephone":"853-28783333"},"isLanyonImport":null,"lanyonImportDataCount":null,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/84\/144084\/201801111515641943619.jpg","poiDistance":null,"poiName":null,"projectCustomTendStrategyList":[{"displayOrder":1,"id":142,"options":[{"displayOrder":1,"id":18,"optionName":"A","strategyId":142,"weightScore":1.00}, {"displayOrder":2,"id":19,"optionName":"B","strategyId":142,"weightScore":1.00}, {"displayOrder":3,"id":20,"optionName":"C","strategyId":142,"weightScore":1.00}],"strategyName":"\u591A\u9009","strategyType":3,"supportStrategyName":0}, {"displayOrder":2,"id":143,"options":null,"strategyName":"\u6587\u672C","strategyType":2,"supportStrategyName":0}, {"displayOrder":3,"id":144,"options":null,"strategyName":"\u662F\u5426","strategyType":1,"supportStrategyName":0}, {"displayOrder":4,"id":145,"options":[{"displayOrder":1,"id":21,"optionName":"\u5355\u9009\u9879\u56DE\u590D","strategyId":145,"weightScore":null}],"strategyName":"\u5355\u9009\u9879\u56DE\u590D","strategyType":4,"supportStrategyName":0}, {"displayOrder":5,"id":146,"options":[{"displayOrder":1,"id":22,"optionName":"\u5355\u9009\u9879\u56DE\u590D1","strategyId":146,"weightScore":null}, {"displayOrder":2,"id":23,"optionName":"\u5355\u9009\u9879\u56DE\u590D2","strategyId":146,"weightScore":null}, {"displayOrder":3,"id":24,"optionName":"\u5355\u9009\u9879\u56DE\u590D3","strategyId":146,"weightScore":null}],"strategyName":"\u5355\u9009\u9879\u56DE\u590Dxxx","strategyType":4,"supportStrategyName":0}],"projectHotelRemarkResponseList":[],"projectHotelTendStrategy":null,"projectId":120,"projectIntentHotelBid":{"bidOrgId":155,"bidOrgType":4,"bidState":1,"bidUploadSource":3,"bidWeight":null,"employRight":null,"employRightFileUrl":null,"hotelBidContactEmail":null,"hotelBidContactMobile":null,"hotelBidContactName":null,"hotelGroupBidContactEmail":null,"hotelGroupBidContactMobile":null,"hotelGroupBidContactName":null,"hotelId":144084,"hotelServicePoints":100.00,"inviteStatus":0,"isUpload":1,"lastYearRoomNight":null,"projectId":120,"projectIntentHotelId":1472,"sendMailStatus":0,"tenderAvgPrice":null},"roomNameList":[{"hotelId":null,"roomId":3281419,"roomName":"Standard twin room"}, {"hotelId":null,"roomId":428125,"roomName":"Standard Room"}, {"hotelId":null,"roomId":3683912,"roomName":"Superior room"}, {"hotelId":null,"roomId":384135,"roomName":"Holiday Superior Room"}, {"hotelId":null,"roomId":384136,"roomName":"Holiday Deluxe Room"}, {"hotelId":null,"roomId":6877120,"roomName":"Superior twin room"}, {"hotelId":null,"roomId":1345199,"roomName":"Standard room"}, {"hotelId":null,"roomId":6877119,"roomName":"Superior king rooms"}, {"hotelId":null,"roomId":1449191,"roomName":"Check into the standard room (regular room) at the holiday front desk"}],"viewCurrencyCode":null,"viewCurrencyExchangeRate":null},"message":"Succeeded","successful":true}  [68ad30933e764e207dc783d9] ------
2025-08-26 11:57:10.976|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68ad30933e764e207dc783d9] ------
2025-08-26 11:57:50.484|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/QueryHotelBidDetail]  [68ad30be3e764e207dc783da] ------
2025-08-26 11:57:50.598|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":null,"projectId":null,"projectIntentHotelId":1472}  [68ad30be3e764e207dc783da] ------
2025-08-26 11:57:52.224|http-nio-6888-exec-3| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelImage, Request Body: {"businessRequest":{"hotelIds":[144084]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelImage","signature":"A5188E52E6CB0628F509AE13C4BED03B","timestamp":1756180672002,"version":"1.0.0"}}  [68ad30be3e764e207dc783da] ------
2025-08-26 11:57:53.792|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sun Aug 31 00:00:00 CST 2025","priceType":1,"startDate":"Fri Aug 01 00:00:00 CST 2025"}],"bidCustomStrategyList":[{"customTendStrategyId":142,"options":[{"isSupport":0,"optionId":18,"optionName":"A"}, {"isSupport":0,"optionId":19,"optionName":"B"}, {"isSupport":0,"optionId":20,"optionName":"C"}],"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u591A\u9009","strategyType":3,"supportStrategyName":null,"supportStrategyText":null}, {"customTendStrategyId":143,"options":null,"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u6587\u672C","strategyType":2,"supportStrategyName":null,"supportStrategyText":null}, {"customTendStrategyId":144,"options":null,"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u662F\u5426","strategyType":1,"supportStrategyName":0,"supportStrategyText":null}, {"customTendStrategyId":145,"options":[{"isSupport":0,"optionId":21,"optionName":"\u5355\u9009\u9879\u56DE\u590D"}],"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u5355\u9009\u9879\u56DE\u590D","strategyType":4,"supportStrategyName":null,"supportStrategyText":null}, {"customTendStrategyId":146,"options":[{"isSupport":0,"optionId":22,"optionName":"\u5355\u9009\u9879\u56DE\u590D1"}, {"isSupport":0,"optionId":23,"optionName":"\u5355\u9009\u9879\u56DE\u590D2"}, {"isSupport":0,"optionId":24,"optionName":"\u5355\u9009\u9879\u56DE\u590D3"}],"projectId":120,"projectIntentHotelGroupId":null,"strategyName":"\u5355\u9009\u9879\u56DE\u590Dxxx","strategyType":4,"supportStrategyName":null,"supportStrategyText":null}],"bidHotelPriceLevelInfoVOList":null,"bidHotelTaxSettings":null,"bidOperateLogList":null,"bidProjectStrategy":{"commission":null,"doAfterLateReserveTime":null,"hasCommission":null,"isIncludeBreakfast":null,"lateReserveTime":null,"supportCancelDay":0,"supportCancelTime":"18:00","supportCheckinInfo":null,"supportIncludeTaxService":null,"supportNoGuarantee":null,"supportPayAtHotel":null,"supportPayEarlyCheckout":null,"supportVccPay":null,"supportWifi":0},"bidUnApplicableDayList":null,"currencyCode":"USD","hotelId":144084,"hotelImageList":[Image(imageId=1533406, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245111112.jpg), Image(imageId=1533446, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245106968.jpg), Image(imageId=1533442, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245039217.jpg), Image(imageId=1533440, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245113523.jpg), Image(imageId=1533430, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245037920.jpg), Image(imageId=1533428, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245037479.jpg), Image(imageId=1533426, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245113221.jpg), Image(imageId=1533424, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245112960.jpg), Image(imageId=1533438, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245038909.jpg), Image(imageId=1533436, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245038663.jpg), Image(imageId=1533434, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245106654.jpg), Image(imageId=1533432, imageType=4, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245038337.jpg), Image(imageId=13908198, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201801111515641949761.jpg), Image(imageId=1533410, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245111743.jpg), Image(imageId=13908195, imageType=2, isMain=1, url=http://fcimage.fangcang.com/images/hotels/84/144084/201801111515641943619.jpg), Image(imageId=1533408, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245111365.jpg), Image(imageId=13908205, imageType=4, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201801111515641963089.jpg), Image(imageId=1533422, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245106231.jpg), Image(imageId=1533418, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245112289.jpg), Image(imageId=13908202, imageType=4, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201801111515641957849.jpg), Image(imageId=1533416, imageType=3, isMain=0, url=http://fcimage.fangcang.com/images/hotels/84/144084/201706241498245111994.jpg)],"hotelInfo":{"cityCode":"MAC","cityName":null,"countryCode":"CN","countryName":null,"currencyCode":"MOP","fitmentDate":"Sat Dec 01 00:00:00 CST 2018","hotelAddress":"No.82-86 Rua de Pequim, Macau","hotelBrandId":10342,"hotelBrandName":"Holiday Inn","hotelGroupId":30060,"hotelId":144084,"hotelName":"Holiday Inn Macau","hotelStar":"49","hotelStarName":"Four-Star Equivalent","isActive":1,"latGoogle":22.**********,"lngGoogle":113.**********,"openingDate":"Fri Jan 01 00:00:00 CST 1993","provinceCode":"MAC","provinceName":null,"rating":"4.1","roomCount":318,"telephone":"853-28783333"},"isLanyonImport":null,"lanyonImportDataCount":null,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/84\/144084\/201801111515641943619.jpg","poiDistance":null,"poiName":null,"projectCustomTendStrategyList":[{"displayOrder":1,"id":142,"options":[{"displayOrder":1,"id":18,"optionName":"A","strategyId":142,"weightScore":1.00}, {"displayOrder":2,"id":19,"optionName":"B","strategyId":142,"weightScore":1.00}, {"displayOrder":3,"id":20,"optionName":"C","strategyId":142,"weightScore":1.00}],"strategyName":"\u591A\u9009","strategyType":3,"supportStrategyName":0}, {"displayOrder":2,"id":143,"options":null,"strategyName":"\u6587\u672C","strategyType":2,"supportStrategyName":0}, {"displayOrder":3,"id":144,"options":null,"strategyName":"\u662F\u5426","strategyType":1,"supportStrategyName":0}, {"displayOrder":4,"id":145,"options":[{"displayOrder":1,"id":21,"optionName":"\u5355\u9009\u9879\u56DE\u590D","strategyId":145,"weightScore":null}],"strategyName":"\u5355\u9009\u9879\u56DE\u590D","strategyType":4,"supportStrategyName":0}, {"displayOrder":5,"id":146,"options":[{"displayOrder":1,"id":22,"optionName":"\u5355\u9009\u9879\u56DE\u590D1","strategyId":146,"weightScore":null}, {"displayOrder":2,"id":23,"optionName":"\u5355\u9009\u9879\u56DE\u590D2","strategyId":146,"weightScore":null}, {"displayOrder":3,"id":24,"optionName":"\u5355\u9009\u9879\u56DE\u590D3","strategyId":146,"weightScore":null}],"strategyName":"\u5355\u9009\u9879\u56DE\u590Dxxx","strategyType":4,"supportStrategyName":0}],"projectHotelRemarkResponseList":[],"projectHotelTendStrategy":null,"projectId":120,"projectIntentHotelBid":{"bidOrgId":155,"bidOrgType":4,"bidState":1,"bidUploadSource":3,"bidWeight":null,"employRight":null,"employRightFileUrl":null,"hotelBidContactEmail":null,"hotelBidContactMobile":null,"hotelBidContactName":null,"hotelGroupBidContactEmail":null,"hotelGroupBidContactMobile":null,"hotelGroupBidContactName":null,"hotelId":144084,"hotelServicePoints":100.00,"inviteStatus":0,"isUpload":1,"lastYearRoomNight":null,"projectId":120,"projectIntentHotelId":1472,"sendMailStatus":0,"tenderAvgPrice":null},"roomNameList":[{"hotelId":null,"roomId":3281419,"roomName":"Standard twin room"}, {"hotelId":null,"roomId":428125,"roomName":"Standard Room"}, {"hotelId":null,"roomId":3683912,"roomName":"Superior room"}, {"hotelId":null,"roomId":384135,"roomName":"Holiday Superior Room"}, {"hotelId":null,"roomId":384136,"roomName":"Holiday Deluxe Room"}, {"hotelId":null,"roomId":6877120,"roomName":"Superior twin room"}, {"hotelId":null,"roomId":1345199,"roomName":"Standard room"}, {"hotelId":null,"roomId":6877119,"roomName":"Superior king rooms"}, {"hotelId":null,"roomId":1449191,"roomName":"Check into the standard room (regular room) at the holiday front desk"}],"viewCurrencyCode":null,"viewCurrencyExchangeRate":null},"message":"Succeeded","successful":true}  [68ad30be3e764e207dc783da] ------
2025-08-26 11:57:54.801|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/QueryHotelBidDetail]  [68ad30be3e764e207dc783da] ------
2025-08-26 11:58:30.509|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/QueryBidHotelPriceLevelList]  [68ad30e63e764e207dc783db] ------
2025-08-26 11:58:30.559|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":144084,"projectIntentHotelId":1472}  [68ad30e63e764e207dc783db] ------
2025-08-26 11:58:31.769|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":8719,"lastOnePersonIncludeTaxPrice":100.00,"lastOnePersonPrice":100.00,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":200.00,"lastTwoPersonPrice":200.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":100.00,"onePersonPrice":100.00,"onePersonPriceTaxInfo":null,"priceType":1,"twoPersonIncludeTaxPrice":200.00,"twoPersonPrice":200.00,"twoPersonPriceTaxInfo":null}, {"hotelPriceId":8720,"lastOnePersonIncludeTaxPrice":200.00,"lastOnePersonPrice":200.00,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":400.00,"lastTwoPersonPrice":400.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":200.00,"onePersonPrice":200.00,"onePersonPriceTaxInfo":null,"priceType":2,"twoPersonIncludeTaxPrice":400.00,"twoPersonPrice":400.00,"twoPersonPriceTaxInfo":null}, {"hotelPriceId":8721,"lastOnePersonIncludeTaxPrice":500.00,"lastOnePersonPrice":500.00,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":1000.00,"lastTwoPersonPrice":1000.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":500.00,"onePersonPrice":500.00,"onePersonPriceTaxInfo":null,"priceType":3,"twoPersonIncludeTaxPrice":1000.00,"twoPersonPrice":1000.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":3772,"hotelPriceLevelId":3781,"isIncludeBreakfast":0,"isLocked":null,"lra":1,"remark":null}],"bigBedRoomCount":null,"currencyCode":"USD","doubleBedRoomCount":null,"exchangeRate":1.0000000000,"hotelPriceLevelId":3781,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":null,"viewCurrencyCode":null,"viewCurrencyExchangeRate":null}],"message":"Succeeded","successful":true}  [68ad30e63e764e207dc783db] ------
2025-08-26 11:58:33.310|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/QueryBidHotelPriceLevelList]  [68ad30e63e764e207dc783db] ------
2025-08-26 12:13:06.816|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 9456 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-26 12:13:06.818|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-26 12:13:08.990|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-26 12:13:08.994|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-26 12:13:09.059|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 45ms. Found 0 Redis repository interfaces.  [] ------
2025-08-26 12:13:10.728|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-26 12:13:10.757|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-26 12:13:10.758|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-26 12:13:11.158|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-26 12:13:11.159|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 4245 ms  [] ------
2025-08-26 12:13:11.642|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-26 12:13:13.886|redisson-netty-2-13| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for ***********/***********:6379  [] ------
2025-08-26 12:13:14.823|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for ***********/***********:6379  [] ------
2025-08-26 12:13:21.368|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-26 12:13:22.614|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-26 12:13:23.319|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-26 12:13:25.394|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-26 12:13:26.067|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-26 12:13:26.068|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-26 12:13:26.177|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-26 12:13:27.648|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-26 12:13:33.828|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-26 12:13:34.589|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 28.665 seconds (JVM running for 31.2)  [] ------
2025-08-26 12:13:34.600|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-26 12:13:34.850|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-26 12:13:37.379|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-26 12:13:38.339|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-26 12:13:38.981|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-26 12:13:39.421|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-26 12:13:40.023|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-26 12:13:57.655|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-26 12:14:14.320|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-26 15:01:11.729|redisson-netty-2-10|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xfd05992c, L:/**********:63657 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-14|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xaec6e084, L:/**********:63678 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-11|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x63f2842b, L:/**********:63664 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-2|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x6ceb05c6, L:/**********:63663 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-4|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x573ad26d, L:/**********:63660 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-30|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x334b07f8, L:/**********:63675 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.730|redisson-netty-2-26|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xe2837526, L:/**********:63668 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-15|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x4f474983, L:/**********:63666 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-5|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x30202730, L:/**********:63658 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-8|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xb96597d7, L:/**********:63661 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.729|redisson-netty-2-31|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xa05627fc, L:/**********:63674 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.733|redisson-netty-2-6|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x2a2da1ba, L:/**********:63662 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.733|redisson-netty-2-29|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x7f3952de, L:/**********:63670 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.730|redisson-netty-2-12|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x14851c3e, L:/**********:63655 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.730|redisson-netty-2-28|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xe720285b, L:/**********:63671 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.730|redisson-netty-2-32|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x989f74b9, L:/**********:63672 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.730|redisson-netty-2-3|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x5947851d, L:/**********:63676 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.734|redisson-netty-2-27|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x00aa26d7, L:/**********:63669 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.734|redisson-netty-2-9|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xccb81892, L:/**********:63656 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.733|redisson-netty-2-24|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xb9aca6a6, L:/**********:63667 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.734|redisson-netty-2-7|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x2b831606, L:/**********:63665 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.734|redisson-netty-2-1|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x0bbc7e65, L:/**********:63673 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.746|redisson-netty-2-15|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x3636de4f, L:/**********:63679 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.745|redisson-netty-2-5|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xababa070, L:/**********:63677 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:01:11.747|redisson-netty-2-3|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x67560b28, L:/**********:63659 - R:***********/***********:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:10:35.170|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 24048 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-26 15:10:35.172|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-26 15:10:37.145|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-26 15:10:37.149|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-26 15:10:37.208|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 40ms. Found 0 Redis repository interfaces.  [] ------
2025-08-26 15:10:38.690|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-26 15:10:38.708|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-26 15:10:38.708|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-26 15:10:38.915|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-26 15:10:38.916|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3650 ms  [] ------
2025-08-26 15:10:39.312|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-26 15:10:41.427|redisson-netty-2-27| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-26 15:10:42.399|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-26 15:10:46.714|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-26 15:10:48.034|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-26 15:10:48.772|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-26 15:10:50.194|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-26 15:10:50.840|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-26 15:10:50.841|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-26 15:10:50.952|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-26 15:10:52.395|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-26 15:10:58.174|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-26 15:10:58.763|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 24.611 seconds (JVM running for 27.33)  [] ------
2025-08-26 15:10:58.773|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-26 15:10:59.010|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-26 15:11:00.142|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-26 15:11:01.078|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-26 15:11:01.742|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-26 15:11:02.131|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-26 15:11:02.529|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-26 15:11:13.531|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-26 15:12:03.211|redisson-netty-2-4|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x9de09482, L:/**********:56178 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.245|redisson-netty-2-6|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x29c0d633, L:/**********:56189 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.623|redisson-netty-2-1|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x159de8fc, L:/**********:56197 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.623|redisson-netty-2-26|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x86e48dec, L:/**********:56193 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.624|redisson-netty-2-16|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x03406feb, L:/**********:56191 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.624|redisson-netty-2-14|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x517a624c, L:/**********:56190 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.626|redisson-netty-2-25|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x0732af24, L:/**********:56192 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.625|redisson-netty-2-30|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x1c95c6d3, L:/**********:56195 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.639|redisson-netty-2-28|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x50f1a567, L:/**********:56194 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.639|redisson-netty-2-29|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xadba5ba9, L:/**********:56196 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.764|redisson-netty-2-32|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x7a049ca6, L:/**********:56199 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.764|redisson-netty-2-31|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x7162d92a, L:/**********:56198 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.875|redisson-netty-2-7|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xe45d8886, L:/**********:56183 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.880|redisson-netty-2-11|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x040d0f4f, L:/**********:56182 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.891|redisson-netty-2-9|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x8a8271bd, L:/**********:56188 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.891|redisson-netty-2-2|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x114c1be8, L:/**********:56187 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.892|redisson-netty-2-3|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x43b57e09, L:/**********:56180 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:03.968|redisson-netty-2-8|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x82afe568, L:/**********:56184 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:04.141|redisson-netty-2-5|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xfd20b224, L:/**********:56201 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:04.141|redisson-netty-2-3|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x4c556d47, L:/**********:56200 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:04.234|redisson-netty-2-13|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xea0557c7, L:/**********:56203 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:04.250|redisson-netty-2-12|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xe80641a2, L:/**********:56202 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:04.611|redisson-netty-2-10|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x8c9c0bc9, L:/**********:56186 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:04.794|pool-2-thread-1|ERROR|o.s.s.support.TaskUtils$LoggingErrorHandler.handleError:95|Unexpected error occurred in scheduled task org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1322678257 [redisClient=[addr=redis://*************:6379], channel=[id: 0xe45d8886, L:/**********:56183 ! R:*************/*************:6379], currentCommand=CommandData [promise=RedissonPromise [promise=ImmediateEventExecutor$ImmediatePromise@23e907f2(failure: org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379])], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]], command: (SMEMBERS), params: [[115, 112, 114, 105, 110, 103, 58, 115, 101, 115, ...]] after 3 retry attempts; nested exception is org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1322678257 [redisClient=[addr=redis://*************:6379], channel=[id: 0xe45d8886, L:/**********:56183 ! R:*************/*************:6379], currentCommand=CommandData [promise=RedissonPromise [promise=ImmediateEventExecutor$ImmediatePromise@23e907f2(failure: org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379])], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]], command: (SMEMBERS), params: [[115, 112, 114, 105, 110, 103, 58, 115, 101, 115, ...]] after 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:195)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:190)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:356)
	at org.redisson.spring.data.connection.RedissonConnection.read(RedissonConnection.java:737)
	at org.redisson.spring.data.connection.RedissonConnection.sMembers(RedissonConnection.java:880)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.DefaultBoundSetOperations.members(DefaultBoundSetOperations.java:152)
	at org.springframework.session.data.redis.RedisSessionExpirationPolicy.cleanExpiredSessions(RedisSessionExpirationPolicy.java:129)
	at org.springframework.session.data.redis.RedisIndexedSessionRepository.cleanupExpiredSessions(RedisIndexedSessionRepository.java:407)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1322678257 [redisClient=[addr=redis://*************:6379], channel=[id: 0xe45d8886, L:/**********:56183 ! R:*************/*************:6379], currentCommand=CommandData [promise=RedissonPromise [promise=ImmediateEventExecutor$ImmediatePromise@23e907f2(failure: org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379])], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]], command: (SMEMBERS), params: [[115, 112, 114, 105, 110, 103, 58, 115, 101, 115, ...]] after 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:278)
	at org.redisson.command.RedisExecutor.access$100(RedisExecutor.java:60)
	at org.redisson.command.RedisExecutor$1.operationComplete(RedisExecutor.java:161)
	at org.redisson.command.RedisExecutor$1.operationComplete(RedisExecutor.java:158)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:552)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:1021)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:882)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1367)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:717)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:764)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1071)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
 [] ------
2025-08-26 15:12:06.188|redisson-netty-2-5|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0x9d4d1585, L:/**********:56179 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:12:08.268|redisson-netty-2-12|ERROR|org.redisson.client.handler.ErrorsLoggingHandler.exceptionCaught:47|Exception occured. Channel: [id: 0xd61a2f94, L:/**********:56181 - R:*************/*************:6379] java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:13:04.765|pool-2-thread-1|ERROR|o.s.s.support.TaskUtils$LoggingErrorHandler.handleError:95|Unexpected error occurred in scheduled task org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1071202561 [redisClient=[addr=redis://*************:6379], channel=[id: 0x82afe568, L:/**********:56184 ! R:*************/*************:6379], currentCommand=CommandData [promise=RedissonPromise [promise=ImmediateEventExecutor$ImmediatePromise@3718b45e(failure: org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379])], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]], command: (SMEMBERS), params: [[115, 112, 114, 105, 110, 103, 58, 115, 101, 115, ...]] after 3 retry attempts; nested exception is org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1071202561 [redisClient=[addr=redis://*************:6379], channel=[id: 0x82afe568, L:/**********:56184 ! R:*************/*************:6379], currentCommand=CommandData [promise=RedissonPromise [promise=ImmediateEventExecutor$ImmediatePromise@3718b45e(failure: org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379])], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]], command: (SMEMBERS), params: [[115, 112, 114, 105, 110, 103, 58, 115, 101, 115, ...]] after 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:195)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:190)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:356)
	at org.redisson.spring.data.connection.RedissonConnection.read(RedissonConnection.java:737)
	at org.redisson.spring.data.connection.RedissonConnection.sMembers(RedissonConnection.java:880)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.DefaultBoundSetOperations.members(DefaultBoundSetOperations.java:152)
	at org.springframework.session.data.redis.RedisSessionExpirationPolicy.cleanExpiredSessions(RedisSessionExpirationPolicy.java:129)
	at org.springframework.session.data.redis.RedisIndexedSessionRepository.cleanupExpiredSessions(RedisIndexedSessionRepository.java:407)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1071202561 [redisClient=[addr=redis://*************:6379], channel=[id: 0x82afe568, L:/**********:56184 ! R:*************/*************:6379], currentCommand=CommandData [promise=RedissonPromise [promise=ImmediateEventExecutor$ImmediatePromise@3718b45e(failure: org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379])], command=(PING), params=[], codec=org.redisson.client.codec.StringCodec]], command: (SMEMBERS), params: [[115, 112, 114, 105, 110, 103, 58, 115, 101, 115, ...]] after 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:278)
	at org.redisson.command.RedisExecutor.access$100(RedisExecutor.java:60)
	at org.redisson.command.RedisExecutor$1.operationComplete(RedisExecutor.java:161)
	at org.redisson.command.RedisExecutor$1.operationComplete(RedisExecutor.java:158)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:552)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:1021)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:882)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1367)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:717)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:764)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1071)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
 [] ------
2025-08-26 15:14:21.243|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-26 15:22:02.592|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-26 15:22:02.592|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-26 15:22:02.609|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 16 ms  [] ------
2025-08-26 15:22:02.971|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68ad609a3e7615ce2c3d2f00] ------
2025-08-26 15:22:03.233|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"projectId":120,"requestSequenceNo":123456}  [68ad609a3e7615ce2c3d2f00] ------
2025-08-26 15:23:50.465|HikariPool-1 housekeeper| WARN|com.zaxxer.hikari.pool.HikariPool.run:787|HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m19s942ms31µs700ns).  [] ------
2025-08-26 15:24:23.080|http-nio-6888-exec-1| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [68ad609a3e7615ce2c3d2f00] ------
2025-08-26 15:24:23.554|http-nio-6888-exec-1| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [68ad609a3e7615ce2c3d2f00] ------
2025-08-26 15:24:23.651|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 140118  [68ad609a3e7615ce2c3d2f00] ------
2025-08-26 15:24:23.661|http-nio-6888-exec-1|ERROR|c.f.grfp.api.exception.CustomRestExceptionHandler.handleException:256|controller error java.lang.NullPointerException
	at com.fangcang.grfp.core.manager.ProjectManager.buildRemarks(ProjectManager.java:499)
	at com.fangcang.grfp.core.manager.ProjectManager.queryHotelGroupTenderPriceList(ProjectManager.java:194)
	at com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService.queryTenderHotelPrice(HotelGroupService.java:670)
	at com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService.exportTenderPrice(HotelGroupService.java:645)
	at com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService$$FastClassBySpringCGLIB$$1c9d7ee9.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService$$EnhancerBySpringCGLIB$$adcf270f.exportTenderPrice(<generated>)
	at com.fangcang.grfp.api.controller.hotelgroup.HotelGroupController.exportTenderPrice(HotelGroupController.java:224)
	at com.fangcang.grfp.api.controller.hotelgroup.HotelGroupController$$FastClassBySpringCGLIB$$69decd4f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice(UserAuditLogAspect.java:55)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.api.controller.hotelgroup.HotelGroupController$$EnhancerBySpringCGLIB$$85ed080e.exportTenderPrice(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fangcang.grfp.core.filter.TraceIdFilter.doFilter(TraceIdFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
  [68ad609a3e7615ce2c3d2f00] ------
2025-08-26 15:24:23.668|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/HotelGroup/ExportTenderPrice FINISH in -1ms returning {"code":"500","data":null,"message":null,"successful":false}  [68ad609a3e7615ce2c3d2f00] ------
2025-08-26 15:24:23.720|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":null,"successful":false}  [] ------
2025-08-26 15:24:24.526|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [] ------
2025-08-26 15:24:51.447|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68ad61433e7615ce2c3d2f01] ------
2025-08-26 15:24:51.505|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"projectId":120,"requestSequenceNo":123456}  [68ad61433e7615ce2c3d2f01] ------
2025-08-26 15:25:15.788|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 24282  [68ad61433e7615ce2c3d2f01] ------
2025-08-26 15:25:15.790|http-nio-6888-exec-5|ERROR|c.f.grfp.api.exception.CustomRestExceptionHandler.handleException:256|controller error java.lang.NullPointerException
	at com.fangcang.grfp.core.manager.ProjectManager.buildRemarks(ProjectManager.java:499)
	at com.fangcang.grfp.core.manager.ProjectManager.queryHotelGroupTenderPriceList(ProjectManager.java:194)
	at com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService.queryTenderHotelPrice(HotelGroupService.java:670)
	at com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService.exportTenderPrice(HotelGroupService.java:645)
	at com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService$$FastClassBySpringCGLIB$$1c9d7ee9.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.fangcang.grfp.api.controller.hotelgroup.service.HotelGroupService$$EnhancerBySpringCGLIB$$adcf270f.exportTenderPrice(<generated>)
	at com.fangcang.grfp.api.controller.hotelgroup.HotelGroupController.exportTenderPrice(HotelGroupController.java:224)
	at com.fangcang.grfp.api.controller.hotelgroup.HotelGroupController$$FastClassBySpringCGLIB$$69decd4f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice(UserAuditLogAspect.java:55)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.api.controller.hotelgroup.HotelGroupController$$EnhancerBySpringCGLIB$$85ed080e.exportTenderPrice(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fangcang.grfp.core.filter.TraceIdFilter.doFilter(TraceIdFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
  [68ad61433e7615ce2c3d2f01] ------
2025-08-26 15:25:15.806|http-nio-6888-exec-5| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/HotelGroup/ExportTenderPrice FINISH in -1ms returning {"code":"500","data":null,"message":null,"successful":false}  [68ad61433e7615ce2c3d2f01] ------
2025-08-26 15:25:15.808|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":null,"successful":false}  [] ------
2025-08-26 15:25:17.015|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [] ------
2025-08-26 15:25:31.771|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 25264 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-26 15:25:31.773|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-26 15:25:33.433|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-26 15:25:33.436|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-26 15:25:33.485|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 33ms. Found 0 Redis repository interfaces.  [] ------
2025-08-26 15:25:34.758|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-26 15:25:34.773|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-26 15:25:34.773|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-26 15:25:34.955|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-26 15:25:34.956|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3111 ms  [] ------
2025-08-26 15:25:35.330|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-26 15:25:37.295|redisson-netty-2-25| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-26 15:25:38.305|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-26 15:25:42.220|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-26 15:25:43.238|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-26 15:25:43.698|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-26 15:25:44.865|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-26 15:25:45.377|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-26 15:25:45.378|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-26 15:25:45.459|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-26 15:25:46.660|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-26 15:25:50.759|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-26 15:25:51.359|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 20.258 seconds (JVM running for 21.929)  [] ------
2025-08-26 15:25:51.370|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-26 15:25:51.593|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-26 15:25:53.485|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-26 15:25:54.500|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-26 15:25:55.101|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-26 15:25:55.552|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-26 15:25:55.941|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-26 15:26:07.243|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-26 15:26:20.332|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-26 15:26:45.149|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-26 15:26:45.149|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-26 15:26:45.164|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 15 ms  [] ------
2025-08-26 15:26:45.541|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68ad61b53e765dea724677cc] ------
2025-08-26 15:26:45.915|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"projectId":120,"requestSequenceNo":123456}  [68ad61b53e765dea724677cc] ------
2025-08-26 15:27:02.943|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.auditlog.UserAuditLogAspect.aroundAdvice:84|audit log: 16727  [68ad61b53e765dea724677cc] ------
2025-08-26 15:27:03.066|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/HotelGroup/ExportTenderPrice]  [68ad61b53e765dea724677cc] ------
2025-08-26 15:28:37.807|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x7d0eb3bc, L:/**********:52249 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.807|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xc75b77eb, L:/**********:52255 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.894|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x2e3fca11, L:/**********:52247 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.895|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xad213e35, L:/**********:52252 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.895|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xedb7304f, L:/**********:52251 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.895|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x5691a917, L:/**********:52248 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.896|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x4241d907, L:/**********:52245 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.897|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x3ca27a36, L:/**********:52250 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.897|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x0bafe879, L:/**********:52253 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.897|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xe7d119d4, L:/**********:52246 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:37.898|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xe36a3ca9, L:/**********:52254 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.191|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x2316fb3c, L:/**********:52257 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.288|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xb11671b8, L:/**********:52258 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.289|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x13ca841e, L:/**********:52260 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.290|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x9a632da5, L:/**********:52259 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.290|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xafd0ee27, L:/**********:52264 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.291|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xa3914e94, L:/**********:52261 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.292|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x987bba46, L:/**********:52263 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.293|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xd6680e89, L:/**********:52262 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.397|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x2388e940, L:/**********:52266 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.398|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xc9312aff, L:/**********:52265 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.788|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x32ec4739, L:/**********:52267 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.788|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0xcee09025, L:/**********:52268 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.888|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x472fefe3, L:/**********:52269 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
2025-08-26 15:28:38.888|redisson-timer-4-1|ERROR|org.redisson.client.handler.PingConnectionHandler.run:89|Unable to send PING command over channel: [id: 0x9182c0cc, L:/**********:52271 - R:*************/*************:6379] org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:219)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:672)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:747)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:472)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
 [] ------
