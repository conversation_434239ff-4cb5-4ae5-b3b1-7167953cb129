package com.fangcang.grfp.core.util;

import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.entity.ProjectIntentHotelEntity;
import com.fangcang.grfp.core.enums.BidUploadSourceEnum;
import com.fangcang.grfp.core.enums.FeeRateTypeEnum;
import com.fangcang.grfp.core.vo.BidHotelPriceTaxValueVO;
import com.fangcang.grfp.core.vo.BidHotelTaxSettingsVO;
import com.fangcang.grfp.core.vo.HotelPriceTaxVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;


/**
 * 报价工具类
 */
public class BidUtil {

    /**
     * 是否Lanyon导入
     * @param projectIntentHotel
     * @return
     */
    public static Integer isLanyonImport(ProjectIntentHotelEntity projectIntentHotel){
        if(projectIntentHotel.getIsUpload() != null && projectIntentHotel.getIsUpload() == RfpConstant.constant_1 && projectIntentHotel.getBidUploadSource() == BidUploadSourceEnum.LANYON.key){
            return RfpConstant.constant_1;
        }
        return RfpConstant.constant_0;
    }


    /**
     * 计算基于基础价格累加的的总固定税费金额
     */
    private static BigDecimal sumFixed(BigDecimal totalFixed, Integer isInclude, Integer feeType, BigDecimal feeValue){
        if(feeValue == null || feeType == null || isInclude == null || !feeType.equals(FeeRateTypeEnum.FIXED.key)){
            return totalFixed;
        }
        boolean include = Objects.equals(isInclude, RfpConstant.constant_1);
        if(include){
            // 价格包含税 累加固定税费
            totalFixed = totalFixed.add(feeValue);
        }
        // 价格包不含税 不考虑
        return totalFixed;
    }

    /**
     * 计算基于基础价格累加的的总百分比税费
     */
    private static BigDecimal sumPercentage(BigDecimal totalPercentage, Integer isInclude, Integer feeType, BigDecimal feeValue){
        if(feeValue == null || feeType == null || isInclude == null || feeType.equals(FeeRateTypeEnum.FIXED.key)){
            return totalPercentage;
        }
        boolean include = Objects.equals(isInclude, RfpConstant.constant_1);
        if(include){
            // 价格包含税 累加基于基础价格的百分比
            totalPercentage = totalPercentage.add(feeValue);
        }
        // 价格不包含税 不考虑
        return totalPercentage;
    }

    public static HotelPriceTaxVO calculatePriceTax(BigDecimal price, BidHotelTaxSettingsVO projectHotelTaxSettings) {
        HotelPriceTaxVO hotelPriceTaxVO = new HotelPriceTaxVO();
        hotelPriceTaxVO.setTotalIncludeTaxAmount(BigDecimal.ZERO);
        hotelPriceTaxVO.setTotalUnIncludeTaxAmount(BigDecimal.ZERO);
        hotelPriceTaxVO.setTotalCostAmount(price);
        if(price == null || price.compareTo(BigDecimal.ZERO) == 0 || projectHotelTaxSettings == null){
            return hotelPriceTaxVO;
        }
        // 设置报价
        hotelPriceTaxVO.setBidPrice(price);

        // 包含总固定税费
        BigDecimal totalFixed = BigDecimal.ZERO;
        // 包含总百分数税费
        BigDecimal totalPercentage = BigDecimal.ZERO;

        // --------------------------------------------------- 计算总Fixed税费和总百分比值 用于计算基础价格(只考虑include=1)
        // 入住税
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getLodgtxFeeIsInclude(), projectHotelTaxSettings.getLodgtxFeeType(), projectHotelTaxSettings.getLodgtxFeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getLodgtxFeeIsInclude(), projectHotelTaxSettings.getLodgtxFeeType(), projectHotelTaxSettings.getLodgtxFeeValue());

        // 国家税
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getStatetxFeeIsInclude(), projectHotelTaxSettings.getStatetxFeeType(), projectHotelTaxSettings.getStatetxFeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getStatetxFeeIsInclude(), projectHotelTaxSettings.getStatetxFeeType(), projectHotelTaxSettings.getStatetxFeeValue());

        // 城市税
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getCitytxFeeIsInclude(), projectHotelTaxSettings.getCitytxFeeType(), projectHotelTaxSettings.getCitytxFeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getCitytxFeeIsInclude(), projectHotelTaxSettings.getCitytxFeeType(), projectHotelTaxSettings.getCitytxFeeValue());

        // 客房增值税
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getVatgstrmFeeIsInclude(), projectHotelTaxSettings.getVatgstrmFeeType(), projectHotelTaxSettings.getVatgstrmFeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getVatgstrmFeeIsInclude(), projectHotelTaxSettings.getVatgstrmFeeType(), projectHotelTaxSettings.getVatgstrmFeeValue());

        // 餐饮增值税
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getVatgstfbFeeIsInclude(), projectHotelTaxSettings.getVatgstfbFeeType(), projectHotelTaxSettings.getVatgstfbFeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getVatgstfbFeeIsInclude(), projectHotelTaxSettings.getVatgstfbFeeType(), projectHotelTaxSettings.getVatgstfbFeeValue());

        // 服务费
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getServiceFeeIsInclude(), projectHotelTaxSettings.getServiceFeeType(), projectHotelTaxSettings.getServiceFeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getServiceFeeIsInclude(), projectHotelTaxSettings.getServiceFeeType(), projectHotelTaxSettings.getServiceFeeValue());

        // 占用费
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getOccFeeIsInclude(), projectHotelTaxSettings.getOccFeeType(), projectHotelTaxSettings.getOccFeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getOccFeeIsInclude(), projectHotelTaxSettings.getOccFeeType(), projectHotelTaxSettings.getOccFeeValue());

        // 其他税费1
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getOthertx1FeeIsInclude(), projectHotelTaxSettings.getOthertx1FeeType(), projectHotelTaxSettings.getOthertx1FeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getOthertx1FeeIsInclude(), projectHotelTaxSettings.getOthertx1FeeType(), projectHotelTaxSettings.getOthertx1FeeValue());

        // 其他税费2
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getOthertx2FeeIsInclude(), projectHotelTaxSettings.getOthertx2FeeType(), projectHotelTaxSettings.getOthertx2FeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getOthertx2FeeIsInclude(), projectHotelTaxSettings.getOthertx2FeeType(), projectHotelTaxSettings.getOthertx2FeeValue());

        // 其他税费3
        totalFixed = sumFixed(totalFixed, projectHotelTaxSettings.getOthertx3FeeIsInclude(), projectHotelTaxSettings.getOthertx3FeeType(), projectHotelTaxSettings.getOthertx3FeeValue());
        totalPercentage = sumPercentage(totalPercentage, projectHotelTaxSettings.getOthertx3FeeIsInclude(), projectHotelTaxSettings.getOthertx3FeeType(), projectHotelTaxSettings.getOthertx3FeeValue());

        /** 计算净房价
          举例：一个酒店报价300SGD  包含入住税费 20SGD   销售税费 10%   增值税费3%
          销售税费=（300-20）/（1+0.1+0.03）*0.1=24.78SGD
          净房价=   （300-20）/（1+0.1+0.03）=247.79
         **/
        // 1. 减去总税费值
        BigDecimal basePrice = price.subtract(totalFixed);

        // 2. 减去百分比
        basePrice = basePrice.divide(BigDecimal.ONE.add(totalPercentage.multiply(new BigDecimal("0.01"))), 2, RoundingMode.HALF_UP);
        hotelPriceTaxVO.setBasePrice(basePrice);

        // 根据净房价格计算税费值
        BidHotelPriceTaxValueVO taxInfo = new BidHotelPriceTaxValueVO();
        BeanUtils.copyProperties(projectHotelTaxSettings, taxInfo);

        // 入住税
        taxInfo.setLodgtxFeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getLodgtxFeeIsInclude(), projectHotelTaxSettings.getLodgtxFeeType(), projectHotelTaxSettings.getLodgtxFeeValue()));
        // 国家税
        taxInfo.setStatetxFeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getStatetxFeeIsInclude(), projectHotelTaxSettings.getStatetxFeeType(), projectHotelTaxSettings.getStatetxFeeValue()));
        // 城市税
        taxInfo.setCitytxFeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getCitytxFeeIsInclude(), projectHotelTaxSettings.getCitytxFeeType(), projectHotelTaxSettings.getCitytxFeeValue()));
        // 客房增值税
        taxInfo.setVatgstrmFeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getVatgstrmFeeIsInclude(), projectHotelTaxSettings.getVatgstrmFeeType(), projectHotelTaxSettings.getVatgstrmFeeValue()));
        // 餐饮增值税
        taxInfo.setVatgstfbFeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getVatgstfbFeeIsInclude(), projectHotelTaxSettings.getVatgstfbFeeType(), projectHotelTaxSettings.getVatgstfbFeeValue()));
        // 服务费
        taxInfo.setServiceFeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getServiceFeeIsInclude(), projectHotelTaxSettings.getServiceFeeType(), projectHotelTaxSettings.getServiceFeeValue()));
        // 占用费
        taxInfo.setOccFeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getOccFeeIsInclude(), projectHotelTaxSettings.getOccFeeType(), projectHotelTaxSettings.getOccFeeValue()));
        // 其他税费1
        taxInfo.setOthertx1FeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getOthertx1FeeIsInclude(), projectHotelTaxSettings.getOthertx1FeeType(), projectHotelTaxSettings.getOthertx1FeeValue()));
        // 其他税费2
        taxInfo.setOthertx2FeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getOthertx2FeeIsInclude(), projectHotelTaxSettings.getOthertx2FeeType(), projectHotelTaxSettings.getOthertx2FeeValue()));
        // 其他税费3
        taxInfo.setOthertx3FeeAmount(calculateFeeAmount(hotelPriceTaxVO, projectHotelTaxSettings.getOthertx3FeeIsInclude(), projectHotelTaxSettings.getOthertx3FeeType(), projectHotelTaxSettings.getOthertx3FeeValue()));

        // 设置税费信息
        hotelPriceTaxVO.setTaxInfo(taxInfo);

        // 计算企业总成本
        hotelPriceTaxVO.setTotalCostAmount(hotelPriceTaxVO.getBidPrice().add(hotelPriceTaxVO.getTotalUnIncludeTaxAmount()));

        return hotelPriceTaxVO;
    }

    private static BigDecimal calculateFeeAmount(HotelPriceTaxVO hotelPriceTaxVO, Integer isInclude, Integer feeType, BigDecimal feeValue) {
        BigDecimal feeAmount = BigDecimal.ZERO;
        if(feeValue == null || feeType == null || isInclude == null){
           return feeAmount;
        } else if(Objects.equals(feeType, FeeRateTypeEnum.FIXED.key)){
        // 如果是固定值 税费为固定金额
           feeAmount = feeValue;
        } else if(Objects.equals(feeType, FeeRateTypeEnum.PERCENTAGE.key)) {
       // 如果是百分比
           feeAmount = hotelPriceTaxVO.getBasePrice().multiply(feeValue.multiply(new BigDecimal("0.01"))).setScale(2, RoundingMode.HALF_UP);
        }
        if(isInclude == RfpConstant.constant_1){
           hotelPriceTaxVO.setTotalIncludeTaxAmount(hotelPriceTaxVO.getTotalIncludeTaxAmount().add(feeAmount));
        } else {
           hotelPriceTaxVO.setTotalUnIncludeTaxAmount(hotelPriceTaxVO.getTotalUnIncludeTaxAmount().add(feeAmount));
        }
        return feeAmount;
    }

    /**
     * 是否有效的 "Y" 或 "N"
     */
    public static boolean validateYN(String value) {
        return StringUtils.isEmpty(value) || ("Y".equals(value) || "N".equals(value));
    }

    /**
     * 是否有效的计费方式, 只能是空或者"P", "F", "N"
     */
    public static boolean validateUom(String value) {
        return StringUtils.isEmpty(value) || ("P".equals(value) || "F".equals(value) || "N".equals(value));
    }

}
