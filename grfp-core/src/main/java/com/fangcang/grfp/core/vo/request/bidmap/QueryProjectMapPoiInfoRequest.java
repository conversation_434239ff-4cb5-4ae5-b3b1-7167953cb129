package com.fangcang.grfp.core.vo.request.bidmap;


import com.fangcang.grfp.core.base.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel("地图查询poi列表统计")
@Getter
@Setter
public class QueryProjectMapPoiInfoRequest extends PageQuery {

    // 项目ID
    @ApiModelProperty("项目ID")
    @NotNull
    private Integer projectId;

    // 距离
    @ApiModelProperty("距离")
    @NotNull
    private Integer distance;

    // 城市编码
    @ApiModelProperty("城市编码")
    private String cityCode;

    // POI名称
    @ApiModelProperty("Poi名称")
    private String poiName;

}
