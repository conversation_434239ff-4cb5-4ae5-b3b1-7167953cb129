package com.fangcang.grfp.core.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class TenderHotelPriceVO extends BaseVO {

    /**
     * 酒店 ID
     */
    private Long hotelId;

    /**
     * 报价状态
     */
    private Integer bidState;
    private String bidStateText;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 酒店名称
     */
    private String hotelName;
    private String hotelNameEnUs;
    private String hotelNameZhCn;

    /**
     * 酒店电话
     */
    private String telephone;

    /**
     * 酒店地址
     */
    private String address;
    private String addressEnUs;
    private String addressZhCn;

    // 城市名称
    private String cityCode;
    private String cityName;

    /**
     * 省份
     */
    private String provinceName;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 酒店集团
     */
    private Long hotelGroupId;
    private String hotelGroupName;

    /**
     * 酒店品牌名称
     */
    private Long hotelBrandId;
    private String hotelBrandName;

    /**
     * 销售人姓名
     */
    private String hotelSalesContactName;

    /**
     * 销售人联系电话
     */
    private String hotelSalesContactMobile;

    /**
     * 销售人邮箱
     */
    private String hotelSalesContactEmail;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 房档1 房型 id
     */
    private String level1RoomTypeIds;

    /**
     * 房档1 房型名称
     */
    private String level1RoomTypeNames;

    // 房档1 房型总间数
    private String level1TotalRoomCount;

    // 房档2 房型
    private String level2RoomTypeIds;

    // 房档2 房型名称
    private String level2RoomTypeNames;

    // 房档2 房型总间数
    private String level2TotalRoomCount;

    // 房档3 房型
    private String level3RoomTypeIds;

    // 房档3 房型名称
    private String level3RoomTypeNames;

    // 房档3 房型总间数
    private String level3TotalRoomCount;

    // 房档4 房型
    private String level4RoomTypeIds;

    // 房档4 房型名称
    private String level4RoomTypeNames;

    // 房档4 房型总间数
    private String level4TotalRoomCount;

    // 房档5 房型
    private String level5RoomTypeIds;

    // 房档5 房型名称
    private String level5RoomTypeNames;

    // 房档5 房型总间数
    private String level5TotalRoomCount;

    /**
     * 协议价开始时间
     */
    private String corpStart;

    /**
     * 协议价结束时间
     */
    private String corpEnd;

    // 房档1

    @ApiModelProperty(value = "LRA_房档1_单人_不含早")
    private BigDecimal lraRt1SglExb;

    @ApiModelProperty(value = "LRA_房档1_单人_含早")
    private BigDecimal lraRt1SglInb;

    @ApiModelProperty(value = "LRA_房档1_双人_不含早")
    private BigDecimal lraRt1DblExb;

    @ApiModelProperty(value = "LRA_房档1_双人_含早")
    private BigDecimal lraRt1DblInb;

    @ApiModelProperty(value = "NLRA_房档1_单人_不含早")
    private BigDecimal nLraRt1SglExb;

    @ApiModelProperty(value = "NLRA_房档1_单人_含早")
    private BigDecimal nLraRt1SglInb;

    @ApiModelProperty(value = "NLRA_房档1_双人_不含早")
    private BigDecimal nLraRt1DblExb;

    @ApiModelProperty(value = "NLRA_房档1_双人_含早")
    private BigDecimal nLraRt1DblInb;

    // 房档2

    @ApiModelProperty(value = "LRA_房档2_单人_不含早")
    private BigDecimal lraRt2SglExb;

    @ApiModelProperty(value = "LRA_房档2_单人_含早")
    private BigDecimal lraRt2SglInb;

    @ApiModelProperty(value = "LRA_房档2_双人_不含早")
    private BigDecimal lraRt2DblExb;

    @ApiModelProperty(value = "LRA_房档2_双人_含早")
    private BigDecimal lraRt2DblInb;

    @ApiModelProperty(value = "NLRA_房档2_单人_不含早")
    private BigDecimal nLraRt2SglExb;

    @ApiModelProperty(value = "NLRA_房档2_单人_含早")
    private BigDecimal nLraRt2SglInb;

    @ApiModelProperty(value = "NLRA_房档2_双人_不含早")
    private BigDecimal nLraRt2DblExb;

    @ApiModelProperty(value = "NLRA_房档2_双人_含早")
    private BigDecimal nLraRt2DblInb;

    // 房档3

    @ApiModelProperty(value = "LRA_房档3_单人_不含早")
    private BigDecimal lraRt3SglExb;

    @ApiModelProperty(value = "LRA_房档3_单人_含早")
    private BigDecimal lraRt3SglInb;

    @ApiModelProperty(value = "LRA_房档3_双人_不含早")
    private BigDecimal lraRt3DblExb;

    @ApiModelProperty(value = "LRA_房档3_双人_含早")
    private BigDecimal lraRt3DblInb;

    @ApiModelProperty(value = "NLRA_房档3_单人_不含早")
    private BigDecimal nLraRt3SglExb;

    @ApiModelProperty(value = "NLRA_房档3_单人_含早")
    private BigDecimal nLraRt3SglInb;

    @ApiModelProperty(value = "NLRA_房档3_双人_不含早")
    private BigDecimal nLraRt3DblExb;

    @ApiModelProperty(value = "NLRA_房档3_双人_含早")
    private BigDecimal nLraRt3DblInb;

    // 房档4

    @ApiModelProperty(value = "LRA_房档4_单人_不含早")
    private BigDecimal lraRt4SglExb;

    @ApiModelProperty(value = "LRA_房档4_单人_含早")
    private BigDecimal lraRt4SglInb;

    @ApiModelProperty(value = "LRA_房档4_双人_不含早")
    private BigDecimal lraRt4DblExb;

    @ApiModelProperty(value = "LRA_房档4_双人_含早")
    private BigDecimal lraRt4DblInb;

    @ApiModelProperty(value = "NLRA_房档4_单人_不含早")
    private BigDecimal nLraRt4SglExb;

    @ApiModelProperty(value = "NLRA_房档4_单人_含早")
    private BigDecimal nLraRt4SglInb;

    @ApiModelProperty(value = "NLRA_房档4_双人_不含早")
    private BigDecimal nLraRt4DblExb;

    @ApiModelProperty(value = "NLRA_房档4_双人_含早")
    private BigDecimal nLraRt4DblInb;

    // 房档5

    @ApiModelProperty(value = "LRA_房档5_单人_不含早")
    private BigDecimal lraRt5SglExb;

    @ApiModelProperty(value = "LRA_房档5_单人_含早")
    private BigDecimal lraRt5SglInb;

    @ApiModelProperty(value = "LRA_房档5_双人_不含早")
    private BigDecimal lraRt5DblExb;

    @ApiModelProperty(value = "LRA_房档5_双人_含早")
    private BigDecimal lraRt5DblInb;

    @ApiModelProperty(value = "NLRA_房档5_单人_不含早")
    private BigDecimal nLraRt5SglExb;

    @ApiModelProperty(value = "NLRA_房档5_单人_含早")
    private BigDecimal nLraRt5SglInb;

    @ApiModelProperty(value = "NLRA_房档5_双人_不含早")
    private BigDecimal nLraRt5DblExb;

    @ApiModelProperty(value = "NLRA_房档5_双人_含早")
    private BigDecimal nLraRt5DblInb;
    
    /**
     * 淡旺季1 开始时间-结束时间 （多段用英文逗号隔开）
     */
    private String season1StartEnd;

    @ApiModelProperty(value = "LRA_房档1_单人_不含早")
    private BigDecimal season1LraRt1SglExb;
    
    @ApiModelProperty(value = "LRA_房档1_单人_含早")
    private BigDecimal season1LraRt1SglInb;
    
    @ApiModelProperty(value = "LRA_房档1_双人_不含早")
    private BigDecimal season1LraRt1DblExb;
    
    @ApiModelProperty(value = "LRA_房档1_双人_含早")
    private BigDecimal season1LraRt1DblInb;

    @ApiModelProperty(value = "NLRA_房档1_单人_不含早")
    private BigDecimal season1NLraRt1SglExb;

    @ApiModelProperty(value = "NLRA_房档1_单人_含早")
    private BigDecimal season1NLraRt1SglInb;

    @ApiModelProperty(value = "NLRA_房档1_双人_不含早")
    private BigDecimal season1NLraRt1DblExb;
    
    @ApiModelProperty(value = "NLRA_房档1_双人_含早")
    private BigDecimal season1NLraRt1DblInb;

    @ApiModelProperty(value = "LRA_房档2_单人_不含早")
    private BigDecimal season1LraRt2SglExb;

    @ApiModelProperty(value = "LRA_房档2_单人_含早")
    private BigDecimal season1LraRt2SglInb;
    
    @ApiModelProperty(value = "LRA_房档2_双人_不含早")
    private BigDecimal season1LraRt2DblExb;

    @ApiModelProperty(value = "LRA_房档2_双人_含早")
    private BigDecimal season1LraRt2DblInb;
    
    @ApiModelProperty(value = "NLRA_房档2_单人_不含早")
    private BigDecimal season1NLraRt2SglExb;
    
    @ApiModelProperty(value = "NLRA_房档2_单人_含早")
    private BigDecimal season1NLraRt2SglInb;

    @ApiModelProperty(value = "NLRA_房档2_双人_不含早")
    private BigDecimal season1NLraRt2DblExb;
    
    @ApiModelProperty(value = "NLRA_房档2_双人_含早")
    private BigDecimal season1NLraRt2DblInb;

    @ApiModelProperty(value = "LRA_房档3_单人_不含早")
    private BigDecimal season1LraRt3SglExb;

    @ApiModelProperty(value = "LRA_房档3_单人_含早")
    private BigDecimal season1LraRt3SglInb;

    @ApiModelProperty(value = "LRA_房档3_双人_不含早")
    private BigDecimal season1LraRt3DblExb;

    @ApiModelProperty(value = "LRA_房档3_双人_含早")
    private BigDecimal season1LraRt3DblInb;

    @ApiModelProperty(value = "NLRA_房档3_单人_不含早")
    private BigDecimal season1NLraRt3SglExb;

    @ApiModelProperty(value = "NLRA_房档3_单人_含早")
    private BigDecimal season1NLraRt3SglInb;

    @ApiModelProperty(value = "NLRA_房档3_双人_不含早")
    private BigDecimal season1NLraRt3DblExb;
    
    @ApiModelProperty(value = "NLRA_房档3_双人_含早")
    private BigDecimal season1NLraRt3DblInb;

    @ApiModelProperty(value = "LRA_房档4_单人_不含早")
    private BigDecimal season1LraRt4SglExb;

    @ApiModelProperty(value = "LRA_房档4_单人_含早")
    private BigDecimal season1LraRt4SglInb;

    @ApiModelProperty(value = "LRA_房档4_双人_不含早")
    private BigDecimal season1LraRt4DblExb;

    @ApiModelProperty(value = "LRA_房档4_双人_含早")
    private BigDecimal season1LraRt4DblInb;

    @ApiModelProperty(value = "NLRA_房档4_单人_不含早")
    private BigDecimal season1NLraRt4SglExb;

    @ApiModelProperty(value = "NLRA_房档4_单人_含早")
    private BigDecimal season1NLraRt4SglInb;

    @ApiModelProperty(value = "NLRA_房档4_双人_不含早")
    private BigDecimal season1NLraRt4DblExb;

    @ApiModelProperty(value = "NLRA_房档4_双人_含早")
    private BigDecimal season1NLraRt4DblInb;

    @ApiModelProperty(value = "LRA_房档5_单人_不含早")
    private BigDecimal season1LraRt5SglExb;

    @ApiModelProperty(value = "LRA_房档5_单人_含早")
    private BigDecimal season1LraRt5SglInb;

    @ApiModelProperty(value = "LRA_房档5_双人_不含早")
    private BigDecimal season1LraRt5DblExb;

    @ApiModelProperty(value = "LRA_房档5_双人_含早")
    private BigDecimal season1LraRt5DblInb;

    @ApiModelProperty(value = "NLRA_房档5_单人_不含早")
    private BigDecimal season1NLraRt5SglExb;

    @ApiModelProperty(value = "NLRA_房档5_单人_含早")
    private BigDecimal season1NLraRt5SglInb;

    @ApiModelProperty(value = "NLRA_房档5_双人_不含早")
    private BigDecimal season1NLraRt5DblExb;

    @ApiModelProperty(value = "NLRA_房档5_双人_含早")
    private BigDecimal season1NLraRt5DblInb;

    /**
     * 淡旺季2 开始时间-结束时间 （多段用英文逗号隔开）
     */
    private String season2StartEnd;

    @ApiModelProperty(value = "LRA_房档1_单人_不含早")
    private BigDecimal season2LraRt1SglExb;

    @ApiModelProperty(value = "LRA_房档1_单人_含早")
    private BigDecimal season2LraRt1SglInb;

    @ApiModelProperty(value = "LRA_房档1_双人_不含早")
    private BigDecimal season2LraRt1DblExb;

    @ApiModelProperty(value = "LRA_房档1_双人_含早")
    private BigDecimal season2LraRt1DblInb;

    @ApiModelProperty(value = "NLRA_房档1_单人_不含早")
    private BigDecimal season2NLraRt1SglExb;

    @ApiModelProperty(value = "NLRA_房档1_单人_含早")
    private BigDecimal season2NLraRt1SglInb;

    @ApiModelProperty(value = "NLRA_房档1_双人_不含早")
    private BigDecimal season2NLraRt1DblExb;

    @ApiModelProperty(value = "NLRA_房档1_双人_含早")
    private BigDecimal season2NLraRt1DblInb;

    @ApiModelProperty(value = "LRA_房档2_单人_不含早")
    private BigDecimal season2LraRt2SglExb;

    @ApiModelProperty(value = "LRA_房档2_单人_含早")
    private BigDecimal season2LraRt2SglInb;

    @ApiModelProperty(value = "LRA_房档2_双人_不含早")
    private BigDecimal season2LraRt2DblExb;

    @ApiModelProperty(value = "LRA_房档2_双人_含早")
    private BigDecimal season2LraRt2DblInb;

    @ApiModelProperty(value = "NLRA_房档2_单人_不含早")
    private BigDecimal season2NLraRt2SglExb;

    @ApiModelProperty(value = "NLRA_房档2_单人_含早")
    private BigDecimal season2NLraRt2SglInb;

    @ApiModelProperty(value = "NLRA_房档2_双人_不含早")
    private BigDecimal season2NLraRt2DblExb;

    @ApiModelProperty(value = "NLRA_房档2_双人_含早")
    private BigDecimal season2NLraRt2DblInb;

    @ApiModelProperty(value = "LRA_房档3_单人_不含早")
    private BigDecimal season2LraRt3SglExb;

    @ApiModelProperty(value = "LRA_房档3_单人_含早")
    private BigDecimal season2LraRt3SglInb;

    @ApiModelProperty(value = "LRA_房档3_双人_不含早")
    private BigDecimal season2LraRt3DblExb;

    @ApiModelProperty(value = "LRA_房档3_双人_含早")
    private BigDecimal season2LraRt3DblInb;

    @ApiModelProperty(value = "NLRA_房档3_单人_不含早")
    private BigDecimal season2NLraRt3SglExb;

    @ApiModelProperty(value = "NLRA_房档3_单人_含早")
    private BigDecimal season2NLraRt3SglInb;

    @ApiModelProperty(value = "NLRA_房档3_双人_不含早")
    private BigDecimal season2NLraRt3DblExb;

    @ApiModelProperty(value = "NLRA_房档3_双人_含早")
    private BigDecimal season2NLraRt3DblInb;

    @ApiModelProperty(value = "LRA_房档4_单人_不含早")
    private BigDecimal season2LraRt4SglExb;

    @ApiModelProperty(value = "LRA_房档4_单人_含早")
    private BigDecimal season2LraRt4SglInb;

    @ApiModelProperty(value = "LRA_房档4_双人_不含早")
    private BigDecimal season2LraRt4DblExb;

    @ApiModelProperty(value = "LRA_房档4_双人_含早")
    private BigDecimal season2LraRt4DblInb;

    @ApiModelProperty(value = "NLRA_房档4_单人_不含早")
    private BigDecimal season2NLraRt4SglExb;

    @ApiModelProperty(value = "NLRA_房档4_单人_含早")
    private BigDecimal season2NLraRt4SglInb;

    @ApiModelProperty(value = "NLRA_房档4_双人_不含早")
    private BigDecimal season2NLraRt4DblExb;

    @ApiModelProperty(value = "NLRA_房档4_双人_含早")
    private BigDecimal season2NLraRt4DblInb;

    @ApiModelProperty(value = "LRA_房档5_单人_不含早")
    private BigDecimal season2LraRt5SglExb;

    @ApiModelProperty(value = "LRA_房档5_单人_含早")
    private BigDecimal season2LraRt5SglInb;

    @ApiModelProperty(value = "LRA_房档5_双人_不含早")
    private BigDecimal season2LraRt5DblExb;

    @ApiModelProperty(value = "LRA_房档5_双人_含早")
    private BigDecimal season2LraRt5DblInb;

    @ApiModelProperty(value = "NLRA_房档5_单人_不含早")
    private BigDecimal season2NLraRt5SglExb;

    @ApiModelProperty(value = "NLRA_房档5_单人_含早")
    private BigDecimal season2NLraRt5SglInb;

    @ApiModelProperty(value = "NLRA_房档5_双人_不含早")
    private BigDecimal season2NLraRt5DblExb;

    @ApiModelProperty(value = "NLRA_房档5_双人_含早")
    private BigDecimal season2NLraRt5DblInb;

    /**
     * 取消策略
     */
    private String cancelPolicy;

    /**
     * 提前入住费用
     */
    private BigDecimal earlyCheckFee;

    /**
     * 提前入住费用收取方式（百分比或固定费用）N 不收 F 固定值 P 百分比
     */
    private String earlyCheckFeeUom;

    /**
     * 报价是否包含提前入住税费
     */
    private String earlyCheckInclude;

    /**
     * 入住税费用
     */
    private BigDecimal lodgtxFee;

    /**
     * 入住税收费方式
     */
    private  String lodgtxUom;

    /**
     * 是否包含入住税
     */
    private String lodgtxInclude;

    /**
     * 州税费用
     */
    private BigDecimal statetxFee;

    /**
     * 州税收费方式
     */
    private String statetxUom;

    /**
     * 是否包含州税
     */
    private String statetxInclude;

    /**
     * 市税费用
     */
    private BigDecimal citytxFee;

    /**
     * 市税收费方式
     */
    private String citytxUom;

    /**
     * 是否包含市税
     */
    private String citytxInclude;

    /**
     * 客房增值税费用
     */
    private BigDecimal vatgstrmFee;

    /**
     * 客房增值税收费方式
     */
    private String vatgstrmUom;

    /**
     * 是否含客房增值税
     */
    private String vatgstrmInclude;

    /**
     * 餐饮增值税费用
     */
    private BigDecimal vatgstfbFee;

    /**
     * 餐饮增值税收费方式
     */
    private String vatgstfbUom;

    /**
     * 是否含餐饮增值税
     */
    private String vatgstfbInclude;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 服务费收费方式
     */
    private String serviceUom;

    /**
     * 是否包含服务费
     */
    private String serviceInclude;

    /**
     * 占用费
     */
    private BigDecimal occFee;

    /**
     * 占用费收费方式
     */
    private String occUom;

    /**
     * 是否包含占用费
     */
    private String occInclude;

    /**
     * 其他税费1
     */
    private BigDecimal otherTxFee1;

    /**
     * 其他税费1收费方式
     */
    private String otherTxFee1Uom;

    /**
     * 其他税费1描述
     */
    private String otherTxFee1Desc;

    /**
     * 其他税费1是否包含
     */
    private String otherTxFee1Incl;

    /**
     * 其他税费2
     */
    private BigDecimal otherTxFee2;

    /**
     * 其他税费2收费方式
     */
    private String otherTxFee2Uom;

    /**
     * 其他税费2描述
     */
    private String otherTxFee2Desc;

    /**
     * 其他税费2是否包含
     */
    private String otherTxFee2Incl;

    /**
     * 其他税费3
     */
    private BigDecimal otherTxFee3;

    /**
     * 其他税费3收费方式
     */
    private String otherTxFee3Uom;

    /**
     * 其他税费3描述
     */
    private String otherTxFee3Desc;

    /**
     * 其他税费3是否包含
     */
    private String otherTxFee3Incl;

    // ==================== 其他服务相关字段 ====================

    /**
     * 是否包含停车费
     */
    private String parkInclude;

    /**
     * 早餐费用
     */
    private BigDecimal breakFee;

    /**
     * 早餐类型
     */
    private String breakType;

    /**
     * 健身中心
     */
    private String fitonCent;

    /**
     * 是否包含无线网络
     */
    private String wirelessInclude;

    /**
     * 费率替代
     */
    private String rateSubs;

    /**
     * 费率个人
     */
    private String ratePerso;

    // 自定义问题
    private String supportCustomBidStrategy1;
    private String supportCustomBidStrategy2;
    private String supportCustomBidStrategy3;
    private String supportCustomBidStrategy4;
    private String supportCustomBidStrategy5;
    private String supportCustomBidStrategy6;
    private String supportCustomBidStrategy7;
    private String supportCustomBidStrategy8;
    private String supportCustomBidStrategy9;
    private String supportCustomBidStrategy10;
    private String supportCustomBidStrategy11;
    private String supportCustomBidStrategy12;
    private String supportCustomBidStrategy13;
    private String supportCustomBidStrategy14;
    private String supportCustomBidStrategy15;
    private String supportCustomBidStrategy16;
    private String supportCustomBidStrategy17;
    private String supportCustomBidStrategy18;
    private String supportCustomBidStrategy19;
    private String supportCustomBidStrategy20;
    private String supportCustomBidStrategy21;
    private String supportCustomBidStrategy22;
    private String supportCustomBidStrategy23;
    private String supportCustomBidStrategy24;
    private String supportCustomBidStrategy25;
    private String supportCustomBidStrategy26;
    private String supportCustomBidStrategy27;
    private String supportCustomBidStrategy28;
    private String supportCustomBidStrategy29;
    private String supportCustomBidStrategy30;
    private String supportCustomBidStrategy31;
    private String supportCustomBidStrategy32;
    private String supportCustomBidStrategy33;
    private String supportCustomBidStrategy34;
    private String supportCustomBidStrategy35;
    private String supportCustomBidStrategy36;
    private String supportCustomBidStrategy37;
    private String supportCustomBidStrategy38;
    private String supportCustomBidStrategy39;
    private String supportCustomBidStrategy40;
    private String supportCustomBidStrategy41;
    private String supportCustomBidStrategy42;
    private String supportCustomBidStrategy43;
    private String supportCustomBidStrategy44;
    private String supportCustomBidStrategy45;
    private String supportCustomBidStrategy46;
    private String supportCustomBidStrategy47;
    private String supportCustomBidStrategy48;
    private String supportCustomBidStrategy49;
    private String supportCustomBidStrategy50;
    private String supportCustomBidStrategy51;
    private String supportCustomBidStrategy52;
    private String supportCustomBidStrategy53;
    private String supportCustomBidStrategy54;
    private String supportCustomBidStrategy55;
    private String supportCustomBidStrategy56;
    private String supportCustomBidStrategy57;
    private String supportCustomBidStrategy58;
    private String supportCustomBidStrategy59;
    private String supportCustomBidStrategy60;
    private String supportCustomBidStrategy61;
    private String supportCustomBidStrategy62;
    private String supportCustomBidStrategy63;
    private String supportCustomBidStrategy64;
    private String supportCustomBidStrategy65;
    private String supportCustomBidStrategy66;
    private String supportCustomBidStrategy67;
    private String supportCustomBidStrategy68;
    private String supportCustomBidStrategy69;
    private String supportCustomBidStrategy70;
    private String supportCustomBidStrategy71;
    private String supportCustomBidStrategy72;
    private String supportCustomBidStrategy73;
    private String supportCustomBidStrategy74;
    private String supportCustomBidStrategy75;
    private String supportCustomBidStrategy76;
    private String supportCustomBidStrategy77;
    private String supportCustomBidStrategy78;
    private String supportCustomBidStrategy79;
    private String supportCustomBidStrategy80;
    private String supportCustomBidStrategy81;
    private String supportCustomBidStrategy82;
    private String supportCustomBidStrategy83;
    private String supportCustomBidStrategy84;
    private String supportCustomBidStrategy85;
    private String supportCustomBidStrategy86;
    private String supportCustomBidStrategy87;
    private String supportCustomBidStrategy88;
    private String supportCustomBidStrategy89;
    private String supportCustomBidStrategy90;
    private String supportCustomBidStrategy91;
    private String supportCustomBidStrategy92;
    private String supportCustomBidStrategy93;
    private String supportCustomBidStrategy94;
    private String supportCustomBidStrategy95;
    private String supportCustomBidStrategy96;
    private String supportCustomBidStrategy97;
    private String supportCustomBidStrategy98;
    private String supportCustomBidStrategy99;
    private String supportCustomBidStrategy100;

    /**
     * 价格不适用时段1_开始
     */
    private String bd1Start;

    /**
     * 价格不适用时段1_结束
     */
    private String bd1End;

    /**
     * 价格不适用时段1_名称
     */
    private String bd1Name;

    /**
     * BD2开始时间
     */
    private String bd2Start;

    /**
     * BD2结束时间
     */
    private String bd2End;

    /**
     * BD2名称
     */
    private String bd2Name;

    /**
     * BD3开始时间
     */
    private String bd3Start;

    /**
     * BD3结束时间
     */
    private String bd3End;

    /**
     * BD3名称
     */
    private String bd3Name;

    /**
     * BD4开始时间
     */
    private String bd4Start;

    /**
     * BD4结束时间
     */
    private String bd4End;

    /**
     * BD4名称
     */
    private String bd4Name;

    /**
     * BD5开始时间
     */
    private String bd5Start;

    /**
     * BD5结束时间
     */
    private String bd5End;

    /**
     * BD5名称
     */
    private String bd5Name;

    /**
     * BD6开始时间
     */
    private String bd6Start;

    /**
     * BD6结束时间
     */
    private String bd6End;

    /**
     * BD6名称
     */
    private String bd6Name;

    /**
     * BD7开始时间
     */
    private String bd7Start;

    /**
     * BD7结束时间
     */
    private String bd7End;

    /**
     * BD7名称
     */
    private String bd7Name;

    /**
     * BD8开始时间
     */
    private String bd8Start;

    /**
     * BD8结束时间
     */
    private String bd8End;

    /**
     * BD8名称
     */
    private String bd8Name;

    /**
     * BD9开始时间
     */
    private String bd9Start;

    /**
     * BD9结束时间
     */
    private String bd9End;

    /**
     * BD9名称
     */
    private String bd9Name;

    /**
     * BD10开始时间
     */
    private String bd10Start;

    /**
     * BD10结束时间
     */
    private String bd10End;

    /**
     * BD10名称
     */
    private String bd10Name;

    /**
     * 干洗服务
     */
    private String laundry;

    /**
     * 洗衣房
     */
    private String laundrySite;

    /**
     * Mini 吧
     */
    private String miniFrig;

}
