package com.fangcang.grfp.core.vo.request.bid;

import com.fangcang.grfp.core.base.BaseVO;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ValidateBidContext extends BaseVO {

    private Integer language;

    private Integer isUpload = YesOrNoEnum.NO.getKey();

    /**
     * 酒店报价联系人
     */
    private String hotelGroupBidContactName;

    /**
     * 酒店报价联系人手机号码
     */
    private String hotelGroupBidContactMobile;

    /**
     * 酒店报价联系人电邮
     */
    private String hotelGroupBidContactEmail;

    /**
     * 机构类型
     */
    private Integer bidOrgType;

    /**
     * 报价机构 id
     */
    private Integer bidOrgId;

}
