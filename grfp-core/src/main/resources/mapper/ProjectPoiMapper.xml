<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.grfp.core.mapper.ProjectPoiMapper">
    <insert id="batchInsert">
        insert into t_project_poi (project_id, poi_id, creator)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.projectId}, #{item.poiId}, #{item.creator})
        </foreach>
        on duplicate key update creator = values(creator)
    </insert>

    <select id="selectPoiInfoByProjectId" resultType="com.fangcang.grfp.core.vo.response.project.ProjectPoiVO">
        select t1.project_id,
               t1.poi_id,
               t2.city_code,
               t3.name_zh_cn cityNameZhCn,
               t3.name_en_us cityNameEnUs,
               t2.poi_name,
               t2.poi_address,
               t2.lng_google,
               t2.lat_google
        from t_project_poi t1
                 inner join t_org_poi t2 on t1.poi_id = t2.poi_id and t2.state = 1
                 inner join t_city t3 on t2.city_code = t3.city_code
        <where>
            t1.project_id = #{projectId}
        <if test="cityCode != null and cityCode != ''">
            AND t2.city_code = #{cityCode}
        </if>

        </where>
    </select>


    <select id="selectMapProjectPoiInfoPage" resultType="com.fangcang.grfp.core.vo.response.bidmap.ProjectPoiInfoResponse">
        select
            pp.project_id as projectId ,
            pp.poi_id as poiId,
            pp.TOTAL_NIGHT_ROOM_COUNT as totalNightRoomCount,
            pp.CITY_NIGHT_ROOM_RISK AS cityRisk,
            pp.TOTAL_AMOUNT as totalAmount,
            op.city_code as cityCode,
            op.poi_name as poiName ,
            op.poi_address as poiAddress ,
            op.lng_google lngGoogle,
            op.lat_google latGoogle,
            op.CITY_CODE as cityCode,
            pp.TOTAL_NIGHT_ROOM_COUNT_5KM as totalNightRoomCount5Km,
            pp.TOTAL_AMOUNT_5KM as totalAmount5Km,
            pp.CITY_NIGHT_ROOM_RISK_5KM as cityRisk5Km,
            pp.TOTAL_NIGHT_ROOM_COUNT_10KM as totalNightRoomCount10Km,
            pp.TOTAL_AMOUNT_10KM as totalAmount10Km,
            pp.CITY_NIGHT_ROOM_RISK_10KM as cityRisk10Km
        from t_project_poi pp ,
            t_org_poi op
        where pp.poi_id = op.poi_id and pp.project_id = #{projectId}
        <if test="poiName != null and poiName != ''">
            AND op.poi_name LIKE CONCAT('%', #{poiName}, '%')
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND op.CITY_CODE = #{cityCode}
        </if>
        ORDER BY
        <if test="distance == 5">
            pp.TOTAL_NIGHT_ROOM_COUNT_5KM  DESC,
        </if>
        <if test="distance == 10">
            pp.TOTAL_NIGHT_ROOM_COUNT_10KM  DESC,
        </if>
        <if test="distance == 0 or distance == 3">
            pp.TOTAL_NIGHT_ROOM_COUNT  DESC,
        </if>
        poiId ASC
    </select>

    <select id="selectMapProjectPoiInfo" resultType="com.fangcang.grfp.core.vo.response.bidmap.ProjectPoiInfoResponse">
        select
        pp.project_id as projectId ,
        pp.poi_id as poiId,
        pp.TOTAL_NIGHT_ROOM_COUNT as totalNightRoomCount,
        pp.CITY_NIGHT_ROOM_RISK AS cityRisk,
        pp.TOTAL_AMOUNT as totalAmount,
        op.city_code as cityCode,
        op.poi_name as poiName ,
        op.poi_address as poiAddress ,
        op.lng_google lngGoogle,
        op.lat_google latGoogle,
        op.CITY_CODE as cityCode,
        pp.TOTAL_NIGHT_ROOM_COUNT_5KM as totalNightRoomCount5Km,
        pp.TOTAL_AMOUNT_5KM as totalAmount5Km,
        pp.CITY_NIGHT_ROOM_RISK_5KM as cityRisk5Km,
        pp.TOTAL_NIGHT_ROOM_COUNT_10KM as totalNightRoomCount10Km,
        pp.TOTAL_AMOUNT_10KM as totalAmount10Km,
        pp.CITY_NIGHT_ROOM_RISK_10KM as cityRisk10Km
        from t_project_poi pp ,
        t_org_poi op
        where pp.poi_id = op.poi_id and pp.project_id = #{projectId}
        <if test="poiId != null">
            AND op.poi_id = #{poiId}
        </if>
        <if test="poiName != null and poiName != ''">
            AND op.poi_name = #{poiName}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND op.CITY_CODE = #{cityCode}
        </if>
        ORDER BY
        <if test="distance == 5">
            pp.TOTAL_NIGHT_ROOM_COUNT_5KM  DESC,
        </if>
        <if test="distance == 10">
            pp.TOTAL_NIGHT_ROOM_COUNT_10KM  DESC,
        </if>
        <if test="distance == 0 or distance == 3">
            pp.TOTAL_NIGHT_ROOM_COUNT  DESC,
        </if>
        poiId ASC
    </select>

    <select id="selectProjectPoiInfo" resultType="com.fangcang.grfp.core.vo.response.bidmap.ProjectPoiInfoResponse">
        select pp.project_id                  as projectid,
               pp.poi_id                      as poiid,
               pp.total_night_room_count      as totalnightroomcount,
               pp.total_amount                as totalamount,
               op.poi_name                    as poiname,
               op.poi_address                 as poiaddress,
               op.lng_google                     lnggoogle,
               op.lat_google                     latgoogle,
               op.city_code                   as citycode,
               pp.total_night_room_count_5km  as totalnightroomcount5km,
               pp.total_amount_5km            as totalamount5km,
               pp.city_night_room_risk_5km    as cityrisk5km,
               pp.total_night_room_count_10km as totalnightroomcount10km,
               pp.total_amount_10km           as totalamount10km,
               pp.city_night_room_risk_10km   as cityrisk10km
        from t_project_poi pp,
             t_org_poi op
        where pp.poi_id = op.poi_id
          and pp.project_id = #{projectId}
    </select>

    <update id="clearCityPoiStat">
        UPDATE t_project_poi
        SET total_night_room_count      = 0,
            total_amount                = 0,
            city_night_room_risk        = 0,
            total_night_room_count_5km  = 0,
            total_amount_5km            = 0,
            city_night_room_risk_5km    = 0,
            total_night_room_count_10km = 0,
            total_amount_10km           = 0,
            city_night_room_risk_10km   = 0
        WHERE PROJECT_ID = #{projectId}
    </update>

    <update id="updateProjectPoiStatInfo">
        UPDATE t_project_poi
        SET total_night_room_count      = #{req.totalNightRoomCount},
            total_amount                = #{req.totalAmount},
            city_night_room_risk        = #{req.cityNightRoomRisk},
            total_night_room_count_5km  = #{req.totalNightRoomCount5km},
            total_amount_5km            = #{req.totalAmount5km},
            city_night_room_risk_5km    = #{req.cityNightRoomRisk5km},
            total_night_room_count_10km = #{req.totalNightRoomCount10km},
            total_amount_10km           = #{req.totalAmount10km},
            city_night_room_risk_10km   = #{req.cityNightRoomRisk10km},
            modifier                    = #{req.modifier}
        WHERE project_id = #{req.projectId}
          AND poi_id = #{req.poiId}
    </update>

    <update id="updateProjectPoi3KmStatInfo">
        UPDATE t_project_poi
        SET poi_hotel_stat_3km = #{projectPoi.poiHotelStat3km},
            MODIFIER           = #{projectPoi.modifier}
        WHERE PROJECT_ID = #{projectPoi.projectId}
          AND POI_ID = #{projectPoi.poiId}
    </update>


</mapper>
