package com.fangcang.grfp.api.controller.project.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fangcang.grfp.core.base.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;


/**
 * 导出项目报价信息
 */
@Getter
@Setter
@ColumnWidth(value = 30)
public class ExportTenderPriceVO extends BaseVO {

    private static final int YELLOW = 13;

    private static final int LIGHT_ORANGE = 52;

    private static final int SKY_BLUE = 40;

    private static final int BRIGHT_GREEN1 = 3;

    /**
     * 报价状态
     */
    @ExcelProperty(value = {"Quotation Status", "EXPORT_FIELD_QUOTATION_STATUS"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String bidStateText;

    /**
     * 备注
     */
    @ExcelProperty(value = {"Remarks", "EXPORT_FIELD_REMARKS"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String remarks;

    /**
     * 酒店集团编码
     */
    @ExcelProperty(value = {"PROPCODE", "EXPORT_FIELD_GROUP_HOTEL_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String propCode;

    /**
     * AMADEUS 连锁编码
     */
    @ExcelProperty(value = {"AMADEUS chain code", "EXPORT_FIELD_GROUP_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String amadeusChainCode;

    /**
     * AMADEUS 酒店编码
     */
    @ExcelProperty(value = {"AMADEUS HOTELCODE", "EXPORT_FIELD_HOTEL_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String amadeusHotelCode;

    /**
     * 酒店名称 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPNAME", "EXPORT_FIELD_HOTEL_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String propName;

    /**
     * 酒店电话 (AMADEUS)
     */
    @ExcelProperty(value = {"PRORPHONE", "EXPORT_FIELD_PHONE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String propPhone;

    /**
     * 酒店地址 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPADD", "EXPORT_FIELD_ADDRESS"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String propAddress;

    /**
     * 酒店城市 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPCITY", "EXPORT_FIELD_CITY"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String propCity;

    /**
     * 酒店省/州 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPSTATEPROV", "EXPORT_FIELD_PROVINCE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String propStateProv;

    /**
     * 酒店国家 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPCOUNTRY", "EXPORT_FIELD_COUNTRY"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String propCountry;


    // ----------------- 酒店基本信息 ------------------

    /**
     * 酒店 id
     */
    @ExcelProperty(value = {"FCPROPCODE", "EXPORT_FIELD_FC_HOTEL_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private Long hotelId;

    /**
     * 酒店名称
     */
    @ExcelProperty(value = {"FCPROPNAME", "EXPORT_FIELD_FC_HOTEL_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String hotelName;

    /**
     * 酒店联系人电话
     */
    @ExcelProperty(value = {"FCPRORPHONE", "EXPORT_FIELD_FC_PHONE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String telephone;

    /**
     * 酒店地址
     */
    @ExcelProperty(value = {"FCPROPADD", "EXPORT_FIELD_FC_ADDRESS"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String address;

    /**
     * 城市名称
     */
    @ExcelProperty(value = {"FCPROPCITY", "EXPORT_FIELD_FC_CITY"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String cityName;

    /**
     * 省/州
     */
    @ExcelProperty(value = {"FCPROPSTATEPROV", "EXPORT_FIELD_FC_PROVINCE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String provinceName;

    /**
     * 国家
     */
    @ExcelProperty(value = {"FCPROPCOUNTRY", "EXPORT_FIELD_FC_COUNTRY"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String countryName;

    /**
     * 酒店集团名称
     */
    @ExcelProperty(value = {"FCHOTELGROUP", "EXPORT_FIELD_HOTEL_GROUP_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String hotelGroupName;

    /**
     * 备注
     */
    @ExcelProperty(value = {"FCHOTELBRAND", "EXPORT_FIELD_HOTEL_BRAND_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String hotelBrandName;

    /**
     * 销售人姓名
     */
    @ExcelProperty(value = {"Sales name", "EXPORT_FIELD_SALES_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String hotelSalesContactName;

    /**
     * 酒店销售邮箱
     */
    @ExcelProperty(value = {"PROPSALESGENREMAIL", "EXPORT_FIELD_SALES_EMAIL"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String hotelSalesContactEmail;

    /**
     * 酒店销售电话
     */
    @ExcelProperty(value = {"Sales mobile", "EXPORT_FIELD_SALES_MOBILE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String hotelSalesContactMobile;

    /**
     * 币种
     */
    @ExcelProperty(value = {"RATE_CURR", "EXPORT_FIELD_CURRENCY_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String currencyCode;

    /**
     * 房型1定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE1DEFINE", "EXPORT_FIELD_ROOM_TYPE_1_DEFINE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level1RoomDefine;

    /**
     * 房仓房型1编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE1CODE", "EXPORT_FIELD_ROOM_TYPE_1_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level1RoomTypeIds;

    /**
     * 房仓房型1名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE1NAME", "EXPORT_FIELD_ROOM_TYPE_1_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level1RoomTypeNames;

    /**
     * 房型1数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE1NUMBER", "EXPORT_FIELD_ROOM_TYPE_1_NUMBER"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level1TotalRoomCount;

    /**
     * 房型2定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE2DEFINE", "EXPORT_FIELD_ROOM_TYPE_2_DEFINE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String roomType2Define;

    /**
     * 房仓房型2编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE2CODE", "EXPORT_FIELD_ROOM_TYPE_2_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level2RoomTypeIds;

    /**
     * 房仓房型2名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE2NAME", "EXPORT_FIELD_ROOM_TYPE_2_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level2RoomTypeNames;

    /**
     * 房型2数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE2NUMBER", "EXPORT_FIELD_ROOM_TYPE_2_NUMBER"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level2TotalRoomCount;

    /**
     * 房型3定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE3DEFINE", "EXPORT_FIELD_ROOM_TYPE_3_DEFINE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String roomType3Define;

    /**
     * 房仓房型3编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE3CODE", "EXPORT_FIELD_ROOM_TYPE_3_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level3RoomTypeIds;

    /**
     * 房仓房型3名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE3NAME", "EXPORT_FIELD_ROOM_TYPE_3_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level3RoomTypeNames;

    /**
     * 房型3数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE3NUMBER", "EXPORT_FIELD_ROOM_TYPE_3_NUMBER"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level3TotalRoomCount;

    /**
     * 房型4定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE4DEFINE", "EXPORT_FIELD_ROOM_TYPE_4_DEFINE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String roomType4Define;

    /**
     * 房仓房型4编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE4CODE", "EXPORT_FIELD_ROOM_TYPE_4_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level4RoomTypeIds;

    /**
     * 房仓房型4名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE4NAME", "EXPORT_FIELD_ROOM_TYPE_4_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level4RoomTypeNames;

    /**
     * 房型4数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE4NUMBER", "EXPORT_FIELD_ROOM_TYPE_4_NUMBER"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level4TotalRoomCount;

    /**
     * 房型5定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE5DEFINE", "EXPORT_FIELD_ROOM_TYPE_5_DEFINE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String roomType5Define;

    /**
     * 房仓房型5编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE5CODE", "EXPORT_FIELD_ROOM_TYPE_5_CODE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level5RoomTypeIds;

    /**
     * 房仓房型5名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE5NAME", "EXPORT_FIELD_ROOM_TYPE_5_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level5RoomTypeNames;

    /**
     * 房型5数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE5NUMBER", "EXPORT_FIELD_ROOM_TYPE_5_NUMBER"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = YELLOW)
    private String level5TotalRoomCount;

    /**
     * 协议价开始时间
     */
    @ExcelProperty(value = {"Corp START", "EXPORT_FIELD_CORP_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String corpStart;

    /**
     * 协议价结束时间
     */
    @ExcelProperty(value = {"Corp END", "EXPORT_FIELD_CORP_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String corpEnd;

    // 房档1
    @ExcelProperty(value = {"LRA_RT1_SGL_EXB", "EXPORT_FIELD_LRA_RT1_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt1SglExb;

    @ExcelProperty(value = {"LRA_RT1_SGL_INB", "EXPORT_FIELD_LRA_RT1_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt1SglInb;

    @ExcelProperty(value = {"LRA_RT1_DBL_EXB", "EXPORT_FIELD_LRA_RT1_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt1DblExb;

    @ExcelProperty(value = {"LRA_RT1_DBL_INB", "EXPORT_FIELD_LRA_RT1_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt1DblInb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_EXB", "EXPORT_FIELD_NLRA_RT1_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt1SglExb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_INB", "EXPORT_FIELD_NLRA_RT1_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt1SglInb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_EXB", "EXPORT_FIELD_NLRA_RT1_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt1DblExb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_INB", "EXPORT_FIELD_NLRA_RT1_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt1DblInb;

    // 房档2

    @ExcelProperty(value = {"LRA_RT2_SGL_EXB", "EXPORT_FIELD_LRA_RT2_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt2SglExb;

    @ExcelProperty(value = {"LRA_RT2_SGL_INB", "EXPORT_FIELD_LRA_RT2_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt2SglInb;

    @ExcelProperty(value = {"LRA_RT2_DBL_EXB", "EXPORT_FIELD_LRA_RT2_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt2DblExb;

    @ExcelProperty(value = {"LRA_RT2_DBL_INB", "EXPORT_FIELD_LRA_RT2_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt2DblInb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_EXB", "EXPORT_FIELD_NLRA_RT2_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt2SglExb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_INB", "EXPORT_FIELD_NLRA_RT2_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt2SglInb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_EXB", "EXPORT_FIELD_NLRA_RT2_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt2DblExb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_INB", "EXPORT_FIELD_NLRA_RT2_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt2DblInb;

    // 房档3

    @ExcelProperty(value = {"LRA_RT3_SGL_EXB", "EXPORT_FIELD_LRA_RT3_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt3SglExb;

    @ExcelProperty(value = {"LRA_RT3_SGL_INB", "EXPORT_FIELD_LRA_RT3_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt3SglInb;

    @ExcelProperty(value = {"LRA_RT3_DBL_EXB", "EXPORT_FIELD_LRA_RT3_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt3DblExb;

    @ExcelProperty(value = {"LRA_RT3_DBL_INB", "EXPORT_FIELD_LRA_RT3_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt3DblInb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_EXB", "EXPORT_FIELD_NLRA_RT3_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt3SglExb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_INB", "EXPORT_FIELD_NLRA_RT3_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt3SglInb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_EXB", "EXPORT_FIELD_NLRA_RT3_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt3DblExb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_INB", "EXPORT_FIELD_NLRA_RT3_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt3DblInb;

    // 房档4

    @ApiModelProperty(value = "LRA_房档4_单人_不含早")
    @ExcelProperty(value = {"LRA_RT4_SGL_EXB", "EXPORT_FIELD_LRA_RT4_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt4SglExb;

    @ApiModelProperty(value = "LRA_房档4_单人_含早")
    @ExcelProperty(value = {"LRA_RT4_SGL_INB", "EXPORT_FIELD_LRA_RT4_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt4SglInb;

    @ApiModelProperty(value = "LRA_房档4_双人_不含早")
    @ExcelProperty(value = {"LRA_RT4_DBL_EXB", "EXPORT_FIELD_LRA_RT4_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt4DblExb;

    @ApiModelProperty(value = "LRA_房档4_双人_含早")
    @ExcelProperty(value = {"LRA_RT4_DBL_INB", "EXPORT_FIELD_LRA_RT4_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt4DblInb;

    @ApiModelProperty(value = "NLRA_房档4_单人_不含早")
    @ExcelProperty(value = {"NLRA_RT4_SGL_EXB", "EXPORT_FIELD_NLRA_RT4_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt4SglExb;

    @ApiModelProperty(value = "NLRA_房档4_单人_含早")
    @ExcelProperty(value = {"NLRA_RT4_SGL_INB", "EXPORT_FIELD_NLRA_RT4_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt4SglInb;

    @ApiModelProperty(value = "NLRA_房档4_双人_不含早")
    @ExcelProperty(value = {"NLRA_RT4_DBL_EXB", "EXPORT_FIELD_NLRA_RT4_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt4DblExb;

    @ApiModelProperty(value = "NLRA_房档4_双人_含早")
    @ExcelProperty(value = {"NLRA_RT4_DBL_INB", "EXPORT_FIELD_NLRA_RT4_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt4DblInb;

    // 房档5

    @ExcelProperty(value = {"LRA_RT5_SGL_EXB", "EXPORT_FIELD_LRA_RT5_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt5SglExb;

    @ExcelProperty(value = {"LRA_RT5_SGL_INB", "EXPORT_FIELD_LRA_RT5_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt5SglInb;

    @ExcelProperty(value = {"LRA_RT5_DBL_EXB", "EXPORT_FIELD_LRA_RT5_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt5DblExb;

    @ExcelProperty(value = {"LRA_RT5_DBL_INB", "EXPORT_FIELD_LRA_RT5_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lraRt5DblInb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_EXB", "EXPORT_FIELD_NLRA_RT5_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt5SglExb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_INB", "EXPORT_FIELD_NLRA_RT5_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt5SglInb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_EXB", "EXPORT_FIELD_NLRA_RT5_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt5DblExb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_INB", "EXPORT_FIELD_NLRA_RT5_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal nLraRt5DblInb;

    /**
     * 淡旺季1开始时间-结束时间 （多段用英文逗号隔开）
     */
    @ExcelProperty(value = {"SEASON1START-END", "EXPORT_FIELD_SEASON1_START_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private String season1StartEnd;

    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    @ExcelProperty(value = {"LRA_RT1_SGL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT1_SGL_EXB"})
    private BigDecimal season1LraRt1SglExb;


    @ExcelProperty(value = {"LRA_RT1_SGL_INB", "EXPORT_FIELD_SEASON1_LRA_RT1_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt1SglInb;

    @ExcelProperty(value = {"LRA_RT1_DBL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT1_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt1DblExb;

    @ExcelProperty(value = {"LRA_RT1_DBL_INB", "EXPORT_FIELD_SEASON1_LRA_RT1_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt1DblInb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT1_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt1SglExb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT1_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt1SglInb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT1_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt1DblExb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT1_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt1DblInb;


    @ExcelProperty(value = {"LRA_RT2_SGL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT2_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt2SglExb;

    @ExcelProperty(value = {"LRA_RT2_SGL_INB", "EXPORT_FIELD_SEASON1_LRA_RT2_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt2SglInb;

    @ExcelProperty(value = {"LRA_RT2_DBL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT2_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt2DblExb;

    @ExcelProperty(value = {"LRA_RT2_DBL_INB", "EXPORT_FIELD_SEASON1_LRA_RT2_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt2DblInb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT2_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt2SglExb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT2_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt2SglInb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT2_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt2DblExb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT2_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt2DblInb;


    @ExcelProperty(value = {"LRA_RT3_SGL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT3_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt3SglExb;

    @ExcelProperty(value = {"LRA_RT3_SGL_INB", "EXPORT_FIELD_SEASON1_LRA_RT3_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt3SglInb;

    @ExcelProperty(value = {"LRA_RT3_DBL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT3_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt3DblExb;

    @ExcelProperty(value = {"LRA_RT3_DBL_INB", "EXPORT_FIELD_SEASON1_LRA_RT3_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt3DblInb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT3_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt3SglExb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT3_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt3SglInb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT3_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt3DblExb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT3_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt3DblInb;


    @ExcelProperty(value = {"LRA_RT4_SGL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT4_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt4SglExb;

    @ExcelProperty(value = {"LRA_RT4_SGL_INB", "EXPORT_FIELD_SEASON1_LRA_RT4_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt4SglInb;

    @ExcelProperty(value = {"LRA_RT4_DBL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT4_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt4DblExb;

    @ExcelProperty(value = {"LRA_RT4_DBL_INB", "EXPORT_FIELD_SEASON1_LRA_RT4_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt4DblInb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT4_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt4SglExb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT4_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt4SglInb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT4_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt4DblExb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT4_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt4DblInb;

    @ExcelProperty(value = {"LRA_RT5_SGL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT5_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt5SglExb;

    @ExcelProperty(value = {"LRA_RT5_SGL_INB", "EXPORT_FIELD_SEASON1_LRA_RT5_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt5SglInb;

    @ExcelProperty(value = {"LRA_RT5_DBL_EXB", "EXPORT_FIELD_SEASON1_LRA_RT5_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt5DblExb;

    @ExcelProperty(value = {"LRA_RT5_DBL_INB", "EXPORT_FIELD_SEASON1_LRA_RT5_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1LraRt5DblInb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT5_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt5SglExb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT5_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt5SglInb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_EXB", "EXPORT_FIELD_SEASON1_NLRA_RT5_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt5DblExb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_INB", "EXPORT_FIELD_SEASON1_NLRA_RT5_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal season1NLraRt5DblInb;

    /**
     * 淡旺季1开始时间-结束时间 （多段用英文逗号隔开）
     */
    @ExcelProperty(value = {"SEASON2START-END", "EXPORT_FIELD_SEASON2_START_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private String season2StartEnd;

    @ExcelProperty(value = {"LRA_RT1_SGL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT1_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt1SglExb;


    @ExcelProperty(value = {"LRA_RT1_SGL_INB", "EXPORT_FIELD_SEASON2_LRA_RT1_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt1SglInb;

    @ExcelProperty(value = {"LRA_RT1_DBL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT1_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt1DblExb;

    @ExcelProperty(value = {"LRA_RT1_DBL_INB", "EXPORT_FIELD_SEASON2_LRA_RT1_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt1DblInb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT1_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt1SglExb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT1_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt1SglInb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT1_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt1DblExb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT1_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt1DblInb;


    @ExcelProperty(value = {"LRA_RT2_SGL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT2_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt2SglExb;

    @ExcelProperty(value = {"LRA_RT2_SGL_INB", "EXPORT_FIELD_SEASON2_LRA_RT2_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt2SglInb;

    @ExcelProperty(value = {"LRA_RT2_DBL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT2_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt2DblExb;

    @ExcelProperty(value = {"LRA_RT2_DBL_INB", "EXPORT_FIELD_SEASON2_LRA_RT2_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt2DblInb;


    @ExcelProperty(value = {"NLRA_RT2_SGL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT2_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt2SglExb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT2_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt2SglInb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT2_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt2DblExb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT2_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt2DblInb;

    @ExcelProperty(value = {"LRA_RT3_SGL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT3_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt3SglExb;

    @ExcelProperty(value = {"LRA_RT3_SGL_INB", "EXPORT_FIELD_SEASON2_LRA_RT3_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt3SglInb;

    @ExcelProperty(value = {"LRA_RT3_DBL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT3_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt3DblExb;

    @ExcelProperty(value = {"LRA_RT3_DBL_INB", "EXPORT_FIELD_SEASON2_LRA_RT3_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt3DblInb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT3_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt3SglExb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT3_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt3SglInb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT3_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt3DblExb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT3_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt3DblInb;


    @ExcelProperty(value = {"LRA_RT4_SGL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT4_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt4SglExb;

    @ExcelProperty(value = {"LRA_RT4_SGL_INB", "EXPORT_FIELD_SEASON2_LRA_RT4_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt4SglInb;

    @ExcelProperty(value = {"LRA_RT4_DBL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT4_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt4DblExb;

    @ExcelProperty(value = {"LRA_RT4_DBL_INB", "EXPORT_FIELD_SEASON2_LRA_RT4_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt4DblInb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT4_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt4SglExb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT4_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt4SglInb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT4_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt4DblExb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT4_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt4DblInb;

    @ExcelProperty(value = {"LRA_RT5_SGL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT5_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt5SglExb;

    @ExcelProperty(value = {"LRA_RT5_SGL_INB", "EXPORT_FIELD_SEASON2_LRA_RT5_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt5SglInb;

    @ExcelProperty(value = {"LRA_RT5_DBL_EXB", "EXPORT_FIELD_SEASON2_LRA_RT5_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt5DblExb;

    @ExcelProperty(value = {"LRA_RT5_DBL_INB", "EXPORT_FIELD_SEASON2_LRA_RT5_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2LraRt5DblInb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT5_SGL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt5SglExb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT5_SGL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt5SglInb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_EXB", "EXPORT_FIELD_SEASON2_NLRA_RT5_DBL_EXB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt5DblExb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_INB", "EXPORT_FIELD_SEASON2_NLRA_RT5_DBL_INB"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal season2NLraRt5DblInb;

    /**
     * 取消政策 (当天以前免费取消用小时数表示 48H)
     */
    @ExcelProperty(value = {"CANC_POL", "EXPORT_FIELD_CANCEL_POLICY"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String cancelPolicy;

    /**
     * 提前入住费用
     */
    @ExcelProperty(value = {"EARLYCK_FEE", "EXPORT_FIELD_EARLY_CHECK_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal earlyCheckFee;

    /**
     * 提前入住费用单位
     */
    @ExcelProperty(value = {"EARLYCK_UOM", "EXPORT_FIELD_EARLY_CHECK_FEE_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String earlyCheckFeeUom;

    /**
     * 提前入住费用是否包含
     */
    @ExcelProperty(value = {"EARLYCK_INCLUDE", "EXPORT_FIELD_EARLY_CHECK_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String earlyCheckInclude;

    /**
     * 入住税费用
     */
    @ExcelProperty(value = {"LODGTX_FEE", "EXPORT_FIELD_LODGTX_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal lodgtxFee;

    /**
     * 入住税收费方式
     */
    @ExcelProperty(value = {"LODGTX_UOM", "EXPORT_FIELD_LODGTX_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private  String lodgtxUom;

    /**
     * 是否包含入住税
     */
    @ExcelProperty(value = {"LODGTX_INCLUDE", "EXPORT_FIELD_LODGTX_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String lodgtxInclude;

    /**
     * 州税费用
     */
    @ExcelProperty(value = {"STATETX_FEE", "EXPORT_FIELD_STATETX_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal statetxFee;

    /**
     * 州税收费方式
     */
    @ExcelProperty(value = {"STATETX_UOM", "EXPORT_FIELD_STATETX_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String statetxUom;

    /**
     * 是否包含州税
     */
    @ExcelProperty(value = {"STATETX_INCLUDE", "EXPORT_FIELD_STATETX_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String statetxInclude;

    /**
     * 市税费用
     */
    @ExcelProperty(value = {"CITYTX_FEE", "EXPORT_FIELD_CITYTX_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal citytxFee;

    /**
     * 市税收费方式
     */
    @ExcelProperty(value = {"CITYTX_UOM", "EXPORT_FIELD_CITYTX_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String citytxUom;

    /**
     * 是否包含市税
     */
    @ExcelProperty(value = {"CITYTX_INCLUDE", "EXPORT_FIELD_CITYTX_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String citytxInclude;

    /**
     * 客房增值税费用
     */
    @ExcelProperty(value = {"VATGSTRM_FEE", "EXPORT_FIELD_VATGSTRM_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal vatgstrmFee;

    /**
     * 客房增值税收费方式
     */
    @ExcelProperty(value = {"VATGSTRM_UOM", "EXPORT_FIELD_VATGSTRM_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String vatgstrmUom;

    /**
     * 是否含客房增值税
     */
    @ExcelProperty(value = {"VATGSTRM_INCLUDE", "EXPORT_FIELD_VATGSTRM_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String vatgstrmInclude;

    /**
     * 餐饮增值税费用
     */
    @ExcelProperty(value = {"VATGSTFB_FEE", "EXPORT_FIELD_VATGSTFB_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal vatgstfbFee;

    /**
     * 餐饮增值税收费方式
     */
    @ExcelProperty(value = {"VATGSTFB_UOM", "EXPORT_FIELD_VATGSTFB_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String vatgstfbUom;

    /**
     * 是否含餐饮增值税
     */
    @ExcelProperty(value = {"VATGSTFB_INCLUDE", "EXPORT_FIELD_VATGSTFB_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String vatgstfbInclude;

    /**
     * 服务费
     */
    @ExcelProperty(value = {"SERVICE_FEE", "EXPORT_FIELD_SERVICE_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal serviceFee;

    /**
     * 服务费单位
     */
    @ExcelProperty(value = {"SERVICE_UOM", "EXPORT_FIELD_SERVICE_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String serviceUom;

    /**
     * 服务费是否包含
     */
    @ExcelProperty(value = {"SERVICE_INCLUDE", "EXPORT_FIELD_SERVICE_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String serviceInclude;

    /**
     * 占用费
     */
    @ExcelProperty(value = {"OCC_FEE", "EXPORT_FIELD_OCC_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal occFee;

    /**
     * 占用费收费方式
     */
    @ExcelProperty(value = {"OCC_UOM", "EXPORT_FIELD_OCC_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String occUom;

    /**
     * 是否包含占用费
     */
    @ExcelProperty(value = {"OCC_INCLUDE", "EXPORT_FIELD_OCC_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String occInclude;

    /**
     * 其他税费1
     */
    @ExcelProperty(value = {"OTHERTX_FEE 1", "EXPORT_FIELD_OTHER_TX_FEE_1"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal otherTxFee1;

    /**
     * 其他税费1收费方式
     */
    @ExcelProperty(value = {"OTHERTX_FEE 1_UOM", "EXPORT_FIELD_OTHER_TX_FEE_1_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private String otherTxFee1Uom;

    /**
     * 其他税费1描述
     */
    @ExcelProperty(value = {"OTHERTX_FEE 1_DESC", "EXPORT_FIELD_OTHER_TX_FEE_1_DESC"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private String otherTxFee1Desc;

    /**
     * 其他税费1是否包含
     */
    @ExcelProperty(value = {"OTHERTX_FEE 1_INCL", "EXPORT_FIELD_OTHER_TX_FEE_1_INCL"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private String otherTxFee1Incl;

    /**
     * 其他税费2
     */
    @ExcelProperty(value = {"OTHERTX_FEE 2", "EXPORT_FIELD_OTHER_TX_FEE_2"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private BigDecimal otherTxFee2;

    /**
     * 其他税费2收费方式
     */
    @ExcelProperty(value = {"OTHERTX_FEE 2_UOM", "EXPORT_FIELD_OTHER_TX_FEE_2_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private String otherTxFee2Uom;

    /**
     * 其他税费2描述
     */
    @ExcelProperty(value = {"OTHERTX_FEE 2_DESC", "EXPORT_FIELD_OTHER_TX_FEE_2_DESC"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private String otherTxFee2Desc;

    /**
     * 其他税费2是否包含
     */
    @ExcelProperty(value = {"OTHERTX_FEE 2_INCL", "EXPORT_FIELD_OTHER_TX_FEE_2_INCL"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = SKY_BLUE)
    private String otherTxFee2Incl;

    /**
     * 其他税费3
     */
    @ExcelProperty(value = {"OTHERTX_FEE 3", "EXPORT_FIELD_OTHER_TX_FEE_3"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private BigDecimal otherTxFee3;

    /**
     * 其他税费3收费方式
     */
    @ExcelProperty(value = {"OTHERTX_FEE 3_UOM", "EXPORT_FIELD_OTHER_TX_FEE_3_UOM"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private String otherTxFee3Uom;

    /**
     * 其他税费3描述
     */
    @ExcelProperty(value = {"OTHERTX_FEE 3_DESC", "EXPORT_FIELD_OTHER_TX_FEE_3_DESC"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private String otherTxFee3Desc;

    /**
     * 其他税费3是否包含
     */
    @ExcelProperty(value = {"OTHERTX_FEE 3_INCL", "EXPORT_FIELD_OTHER_TX_FEE_3_INCL"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = BRIGHT_GREEN1)
    private String otherTxFee3Incl;

    // ==================== 其他服务相关字段 ====================

    /**
     * 是否包含停车费
     */
    @ExcelProperty(value = {"PARK_INCLUDE", "EXPORT_FIELD_PARK_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String parkInclude;

    /**
     * 早餐费用
     */
    @ExcelProperty(value = {"BREAK_FEE", "EXPORT_FIELD_BREAK_FEE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private BigDecimal breakFee;

    /**
     * 早餐类型
     */
    @ExcelProperty(value = {"BREAK_TYPE", "EXPORT_FIELD_BREAK_TYPE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String breakType;

    /**
     * 健身中心
     */
    @ExcelProperty(value = {"FITON_CENT", "EXPORT_FIELD_FITON_CENT"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String fitonCent;

    /**
     * 是否包含无线网络
     */
    @ExcelProperty(value = {"WIRELESS_INCLUDE", "EXPORT_FIELD_WIRELESS_INCLUDE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String wirelessInclude;

    /**
     * 费率替代
     */
    @ExcelProperty(value = {"RATE_SUBS", "EXPORT_FIELD_RATE_SUBS"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String rateSubs;

    /**
     * 费率个人
     */
    @ExcelProperty(value = {"RATE_PERSO", "EXPORT_FIELD_RATE_PERSO"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String ratePerso;


    // 自定义问题
    @ExcelProperty(value = {"USERDEFINED1", "EXPORT_FIELD_CUSTOM_STRATEGY_1"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy1;

    @ExcelProperty(value = {"USERDEFINED2", "EXPORT_FIELD_CUSTOM_STRATEGY_2"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy2;

    @ExcelProperty(value = {"USERDEFINED3", "EXPORT_FIELD_CUSTOM_STRATEGY_3"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy3;

    @ExcelProperty(value = {"USERDEFINED4", "EXPORT_FIELD_CUSTOM_STRATEGY_4"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy4;

    @ExcelProperty(value = {"USERDEFINED5", "EXPORT_FIELD_CUSTOM_STRATEGY_5"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy5;

    @ExcelProperty(value = {"USERDEFINED6", "EXPORT_FIELD_CUSTOM_STRATEGY_6"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy6;

    @ExcelProperty(value = {"USERDEFINED7", "EXPORT_FIELD_CUSTOM_STRATEGY_7"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy7;

    @ExcelProperty(value = {"USERDEFINED8", "EXPORT_FIELD_CUSTOM_STRATEGY_8"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy8;

    @ExcelProperty(value = {"USERDEFINED9", "EXPORT_FIELD_CUSTOM_STRATEGY_9"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy9;

    @ExcelProperty(value = {"USERDEFINED10", "EXPORT_FIELD_CUSTOM_STRATEGY_10"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy10;

    @ExcelProperty(value = {"USERDEFINED11", "EXPORT_FIELD_CUSTOM_STRATEGY_11"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy11;

    @ExcelProperty(value = {"USERDEFINED12", "EXPORT_FIELD_CUSTOM_STRATEGY_12"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy12;

    @ExcelProperty(value = {"USERDEFINED13", "EXPORT_FIELD_CUSTOM_STRATEGY_13"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy13;

    @ExcelProperty(value = {"USERDEFINED14", "EXPORT_FIELD_CUSTOM_STRATEGY_14"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy14;

    @ExcelProperty(value = {"USERDEFINED15", "EXPORT_FIELD_CUSTOM_STRATEGY_15"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy15;

    @ExcelProperty(value = {"USERDEFINED16", "EXPORT_FIELD_CUSTOM_STRATEGY_16"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy16;

    @ExcelProperty(value = {"USERDEFINED17", "EXPORT_FIELD_CUSTOM_STRATEGY_17"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy17;

    @ExcelProperty(value = {"USERDEFINED18", "EXPORT_FIELD_CUSTOM_STRATEGY_18"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy18;

    @ExcelProperty(value = {"USERDEFINED19", "EXPORT_FIELD_CUSTOM_STRATEGY_19"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy19;

    @ExcelProperty(value = {"USERDEFINED20", "EXPORT_FIELD_CUSTOM_STRATEGY_20"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy20;

    @ExcelProperty(value = {"USERDEFINED21", "EXPORT_FIELD_CUSTOM_STRATEGY_21"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy21;

    @ExcelProperty(value = {"USERDEFINED22", "EXPORT_FIELD_CUSTOM_STRATEGY_22"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy22;

    @ExcelProperty(value = {"USERDEFINED23", "EXPORT_FIELD_CUSTOM_STRATEGY_23"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy23;

    @ExcelProperty(value = {"USERDEFINED24", "EXPORT_FIELD_CUSTOM_STRATEGY_24"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy24;

    @ExcelProperty(value = {"USERDEFINED25", "EXPORT_FIELD_CUSTOM_STRATEGY_25"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy25;

    @ExcelProperty(value = {"USERDEFINED26", "EXPORT_FIELD_CUSTOM_STRATEGY_26"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy26;

    @ExcelProperty(value = {"USERDEFINED27", "EXPORT_FIELD_CUSTOM_STRATEGY_27"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy27;

    @ExcelProperty(value = {"USERDEFINED28", "EXPORT_FIELD_CUSTOM_STRATEGY_28"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy28;

    @ExcelProperty(value = {"USERDEFINED29", "EXPORT_FIELD_CUSTOM_STRATEGY_29"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy29;

    @ExcelProperty(value = {"USERDEFINED30", "EXPORT_FIELD_CUSTOM_STRATEGY_30"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy30;

    @ExcelProperty(value = {"USERDEFINED31", "EXPORT_FIELD_CUSTOM_STRATEGY_31"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy31;

    @ExcelProperty(value = {"USERDEFINED32", "EXPORT_FIELD_CUSTOM_STRATEGY_32"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy32;

    @ExcelProperty(value = {"USERDEFINED33", "EXPORT_FIELD_CUSTOM_STRATEGY_33"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy33;

    @ExcelProperty(value = {"USERDEFINED34", "EXPORT_FIELD_CUSTOM_STRATEGY_34"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy34;

    @ExcelProperty(value = {"USERDEFINED35", "EXPORT_FIELD_CUSTOM_STRATEGY_35"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy35;

    @ExcelProperty(value = {"USERDEFINED36", "EXPORT_FIELD_CUSTOM_STRATEGY_36"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy36;

    @ExcelProperty(value = {"USERDEFINED37", "EXPORT_FIELD_CUSTOM_STRATEGY_37"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy37;

    @ExcelProperty(value = {"USERDEFINED38", "EXPORT_FIELD_CUSTOM_STRATEGY_38"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy38;

    @ExcelProperty(value = {"USERDEFINED39", "EXPORT_FIELD_CUSTOM_STRATEGY_39"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy39;

    @ExcelProperty(value = {"USERDEFINED40", "EXPORT_FIELD_CUSTOM_STRATEGY_40"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy40;

    @ExcelProperty(value = {"USERDEFINED41", "EXPORT_FIELD_CUSTOM_STRATEGY_41"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy41;

    @ExcelProperty(value = {"USERDEFINED42", "EXPORT_FIELD_CUSTOM_STRATEGY_42"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy42;

    @ExcelProperty(value = {"USERDEFINED43", "EXPORT_FIELD_CUSTOM_STRATEGY_43"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy43;

    @ExcelProperty(value = {"USERDEFINED44", "EXPORT_FIELD_CUSTOM_STRATEGY_44"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy44;

    @ExcelProperty(value = {"USERDEFINED45", "EXPORT_FIELD_CUSTOM_STRATEGY_45"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy45;

    @ExcelProperty(value = {"USERDEFINED46", "EXPORT_FIELD_CUSTOM_STRATEGY_46"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy46;

    @ExcelProperty(value = {"USERDEFINED47", "EXPORT_FIELD_CUSTOM_STRATEGY_47"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy47;

    @ExcelProperty(value = {"USERDEFINED48", "EXPORT_FIELD_CUSTOM_STRATEGY_48"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy48;

    @ExcelProperty(value = {"USERDEFINED49", "EXPORT_FIELD_CUSTOM_STRATEGY_49"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy49;

    @ExcelProperty(value = {"USERDEFINED50", "EXPORT_FIELD_CUSTOM_STRATEGY_50"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy50;

    @ExcelProperty(value = {"USERDEFINED51", "EXPORT_FIELD_CUSTOM_STRATEGY_51"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy51;

    @ExcelProperty(value = {"USERDEFINED52", "EXPORT_FIELD_CUSTOM_STRATEGY_52"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy52;

    @ExcelProperty(value = {"USERDEFINED53", "EXPORT_FIELD_CUSTOM_STRATEGY_53"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy53;

    @ExcelProperty(value = {"USERDEFINED54", "EXPORT_FIELD_CUSTOM_STRATEGY_54"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy54;

    @ExcelProperty(value = {"USERDEFINED55", "EXPORT_FIELD_CUSTOM_STRATEGY_55"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy55;

    @ExcelProperty(value = {"USERDEFINED56", "EXPORT_FIELD_CUSTOM_STRATEGY_56"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy56;

    @ExcelProperty(value = {"USERDEFINED57", "EXPORT_FIELD_CUSTOM_STRATEGY_57"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy57;

    @ExcelProperty(value = {"USERDEFINED58", "EXPORT_FIELD_CUSTOM_STRATEGY_58"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy58;

    @ExcelProperty(value = {"USERDEFINED59", "EXPORT_FIELD_CUSTOM_STRATEGY_59"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy59;

    @ExcelProperty(value = {"USERDEFINED60", "EXPORT_FIELD_CUSTOM_STRATEGY_60"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy60;

    @ExcelProperty(value = {"USERDEFINED61", "EXPORT_FIELD_CUSTOM_STRATEGY_61"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy61;

    @ExcelProperty(value = {"USERDEFINED62", "EXPORT_FIELD_CUSTOM_STRATEGY_62"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy62;

    @ExcelProperty(value = {"USERDEFINED63", "EXPORT_FIELD_CUSTOM_STRATEGY_63"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy63;

    @ExcelProperty(value = {"USERDEFINED64", "EXPORT_FIELD_CUSTOM_STRATEGY_64"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy64;

    @ExcelProperty(value = {"USERDEFINED65", "EXPORT_FIELD_CUSTOM_STRATEGY_65"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy65;

    @ExcelProperty(value = {"USERDEFINED66", "EXPORT_FIELD_CUSTOM_STRATEGY_66"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy66;

    @ExcelProperty(value = {"USERDEFINED67", "EXPORT_FIELD_CUSTOM_STRATEGY_67"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy67;

    @ExcelProperty(value = {"USERDEFINED68", "EXPORT_FIELD_CUSTOM_STRATEGY_68"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy68;

    @ExcelProperty(value = {"USERDEFINED69", "EXPORT_FIELD_CUSTOM_STRATEGY_69"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy69;

    @ExcelProperty(value = {"USERDEFINED70", "EXPORT_FIELD_CUSTOM_STRATEGY_70"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy70;

    @ExcelProperty(value = {"USERDEFINED71", "EXPORT_FIELD_CUSTOM_STRATEGY_71"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy71;

    @ExcelProperty(value = {"USERDEFINED72", "EXPORT_FIELD_CUSTOM_STRATEGY_72"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy72;

    @ExcelProperty(value = {"USERDEFINED73", "EXPORT_FIELD_CUSTOM_STRATEGY_73"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy73;

    @ExcelProperty(value = {"USERDEFINED74", "EXPORT_FIELD_CUSTOM_STRATEGY_74"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy74;

    @ExcelProperty(value = {"USERDEFINED75", "EXPORT_FIELD_CUSTOM_STRATEGY_75"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy75;

    @ExcelProperty(value = {"USERDEFINED76", "EXPORT_FIELD_CUSTOM_STRATEGY_76"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy76;

    @ExcelProperty(value = {"USERDEFINED77", "EXPORT_FIELD_CUSTOM_STRATEGY_77"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy77;

    @ExcelProperty(value = {"USERDEFINED78", "EXPORT_FIELD_CUSTOM_STRATEGY_78"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy78;

    @ExcelProperty(value = {"USERDEFINED79", "EXPORT_FIELD_CUSTOM_STRATEGY_79"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy79;

    @ExcelProperty(value = {"USERDEFINED80", "EXPORT_FIELD_CUSTOM_STRATEGY_80"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy80;

    @ExcelProperty(value = {"USERDEFINED81", "EXPORT_FIELD_CUSTOM_STRATEGY_81"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy81;

    @ExcelProperty(value = {"USERDEFINED82", "EXPORT_FIELD_CUSTOM_STRATEGY_82"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy82;

    @ExcelProperty(value = {"USERDEFINED83", "EXPORT_FIELD_CUSTOM_STRATEGY_83"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy83;

    @ExcelProperty(value = {"USERDEFINED84", "EXPORT_FIELD_CUSTOM_STRATEGY_84"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy84;

    @ExcelProperty(value = {"USERDEFINED85", "EXPORT_FIELD_CUSTOM_STRATEGY_85"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy85;

    @ExcelProperty(value = {"USERDEFINED86", "EXPORT_FIELD_CUSTOM_STRATEGY_86"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy86;

    @ExcelProperty(value = {"USERDEFINED87", "EXPORT_FIELD_CUSTOM_STRATEGY_87"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy87;

    @ExcelProperty(value = {"USERDEFINED88", "EXPORT_FIELD_CUSTOM_STRATEGY_88"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy88;

    @ExcelProperty(value = {"USERDEFINED89", "EXPORT_FIELD_CUSTOM_STRATEGY_89"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy89;

    @ExcelProperty(value = {"USERDEFINED90", "EXPORT_FIELD_CUSTOM_STRATEGY_90"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy90;

    @ExcelProperty(value = {"USERDEFINED91", "EXPORT_FIELD_CUSTOM_STRATEGY_91"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy91;

    @ExcelProperty(value = {"USERDEFINED92", "EXPORT_FIELD_CUSTOM_STRATEGY_92"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy92;

    @ExcelProperty(value = {"USERDEFINED93", "EXPORT_FIELD_CUSTOM_STRATEGY_93"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy93;

    @ExcelProperty(value = {"USERDEFINED94", "EXPORT_FIELD_CUSTOM_STRATEGY_94"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy94;

    @ExcelProperty(value = {"USERDEFINED95", "EXPORT_FIELD_CUSTOM_STRATEGY_95"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy95;

    @ExcelProperty(value = {"USERDEFINED96", "EXPORT_FIELD_CUSTOM_STRATEGY_96"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy96;

    @ExcelProperty(value = {"USERDEFINED97", "EXPORT_FIELD_CUSTOM_STRATEGY_97"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy97;

    @ExcelProperty(value = {"USERDEFINED98", "EXPORT_FIELD_CUSTOM_STRATEGY_98"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy98;

    @ExcelProperty(value = {"USERDEFINED99", "EXPORT_FIELD_CUSTOM_STRATEGY_99"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy99;

    @ExcelProperty(value = {"USERDEFINED100", "EXPORT_FIELD_CUSTOM_STRATEGY_100"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String supportCustomBidStrategy100;

    /**
     * 价格不适用时段1_开始
     */
    @ExcelProperty(value = {"BD1_START", "EXPORT_FIELD_BD1_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd1Start;

    /**
     * 价格不适用时段1_结束
     */
    @ExcelProperty(value = {"BD1_END", "EXPORT_FIELD_BD1_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd1End;

    /**
     * 价格不适用时段1_名称
     */
    @ExcelProperty(value = {"BD1_NAME", "EXPORT_FIELD_BD1_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd1Name;

    /**
     * BD2开始时间
     */
    @ExcelProperty(value = {"BD2_START", "EXPORT_FIELD_BD2_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd2Start;

    /**
     * BD2结束时间
     */
    @ExcelProperty(value = {"BD2_END", "EXPORT_FIELD_BD2_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd2End;

    /**
     * BD2名称
     */
    @ExcelProperty(value = {"BD2_NAME", "EXPORT_FIELD_BD2_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd2Name;

    /**
     * BD3开始时间
     */
    @ExcelProperty(value = {"BD3_START", "EXPORT_FIELD_BD3_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd3Start;

    /**
     * BD3结束时间
     */
    @ExcelProperty(value = {"BD3_END", "EXPORT_FIELD_BD3_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd3End;

    /**
     * BD3名称
     */
    @ExcelProperty(value = {"BD3_NAME", "EXPORT_FIELD_BD3_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd3Name;

    /**
     * BD4开始时间
     */
    @ExcelProperty(value = {"BD4_START", "EXPORT_FIELD_BD4_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd4Start;

    /**
     * BD4结束时间
     */
    @ExcelProperty(value = {"BD4_END", "EXPORT_FIELD_BD4_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd4End;

    /**
     * BD4名称
     */
    @ExcelProperty(value = {"BD4_NAME", "EXPORT_FIELD_BD4_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd4Name;

    /**
     * BD5开始时间
     */
    @ExcelProperty(value = {"BD5_START", "EXPORT_FIELD_BD5_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd5Start;

    /**
     * BD5结束时间
     */
    @ExcelProperty(value = {"BD5_END", "EXPORT_FIELD_BD5_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd5End;

    /**
     * BD5名称
     */
    @ExcelProperty(value = {"BD5_NAME", "EXPORT_FIELD_BD5_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd5Name;

    /**
     * BD6开始时间
     */
    @ExcelProperty(value = {"BD6_START", "EXPORT_FIELD_BD6_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd6Start;

    /**
     * BD6结束时间
     */
    @ExcelProperty(value = {"BD6_END", "EXPORT_FIELD_BD6_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd6End;

    /**
     * BD6名称
     */
    @ExcelProperty(value = {"BD6_NAME", "EXPORT_FIELD_BD6_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd6Name;

    /**
     * BD7开始时间
     */
    @ExcelProperty(value = {"BD7_START", "EXPORT_FIELD_BD7_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd7Start;

    /**
     * BD7结束时间
     */
    @ExcelProperty(value = {"BD7_END", "EXPORT_FIELD_BD7_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd7End;

    /**
     * BD7名称
     */
    @ExcelProperty(value = {"BD7_NAME", "EXPORT_FIELD_BD7_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd7Name;

    /**
     * BD8开始时间
     */
    @ExcelProperty(value = {"BD8_START", "EXPORT_FIELD_BD8_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd8Start;

    /**
     * BD8结束时间
     */
    @ExcelProperty(value = {"BD8_END", "EXPORT_FIELD_BD8_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd8End;

    /**
     * BD8名称
     */
    @ExcelProperty(value = {"BD8_NAME", "EXPORT_FIELD_BD8_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd8Name;

    /**
     * BD9开始时间
     */
    @ExcelProperty(value = {"BD9_START", "EXPORT_FIELD_BD9_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd9Start;

    /**
     * BD9结束时间
     */
    @ExcelProperty(value = {"BD9_END", "EXPORT_FIELD_BD9_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd9End;

    /**
     * BD9名称
     */
    @ExcelProperty(value = {"BD9_NAME", "EXPORT_FIELD_BD9_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd9Name;

    /**
     * BD10开始时间
     */
    @ExcelProperty(value = {"BD10_START", "EXPORT_FIELD_BD10_START"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd10Start;

    /**
     * BD10结束时间
     */
    @ExcelProperty(value = {"BD10_END", "EXPORT_FIELD_BD10_END"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd10End;

    /**
     * BD10名称
     */
    @ExcelProperty(value = {"BD10_NAME", "EXPORT_FIELD_BD10_NAME"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String bd10Name;

    /**
     * 干洗服务
     */
    @ExcelProperty(value = {"LAUN_DRY", "EXPORT_FIELD_LAUN_DRY"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String laundry;

    /**
     * 洗衣房
     */
    @ExcelProperty(value = {"LAUNDRY_SITE", "EXPORT_FIELD_LAUNDRY_SITE"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String laundrySite;

    /**
     * Mini 吧
     */
    @ExcelProperty(value = {"MINI_FRIG", "EXPORT_FIELD_MINI_FRIG"})
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = LIGHT_ORANGE)
    private String miniFrig;

}